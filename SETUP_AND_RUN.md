# تشغيل منصة التجارة الإلكترونية الذكية

## المتطلبات الأساسية

### 1. تثبيت Python
- **Windows**: قم بتحميل Python 3.8+ من [python.org](https://python.org)
- **macOS**: استخدم `brew install python3` أو قم بالتحميل من الموقع الرسمي
- **Linux**: استخدم `sudo apt install python3 python3-pip` (Ubuntu/Debian)

### 2. التحقق من التثبيت
```bash
python --version
# أو
python3 --version
```

## خطوات التشغيل

### الطريقة الأولى: استخدام الملفات المساعدة

#### على Windows:
```cmd
# انقر نقراً مزدوجاً على الملف أو شغله من Command Prompt
run_server.bat
```

#### على macOS/Linux:
```bash
# اجعل الملف قابل للتنفيذ
chmod +x run_server.sh

# شغل الملف
./run_server.sh
```

### الطريقة الثانية: التشغيل اليدوي

#### 1. إنشاء البيئة الافتراضية
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# macOS/Linux
python3 -m venv venv
source venv/bin/activate
```

#### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

#### 3. إعداد قاعدة البيانات
```bash
python manage.py makemigrations
python manage.py migrate
```

#### 4. إنشاء مستخدم إداري (اختياري)
```bash
python manage.py createsuperuser
```

#### 5. تشغيل الخادم
```bash
python manage.py runserver 0.0.0.0:8000
```

## الوصول للمنصة

بعد تشغيل الخادم، يمكنك الوصول للمنصة عبر:

### الواجهات الرئيسية:
- **الصفحة الرئيسية**: http://127.0.0.1:8000/
- **البحث عن المنتجات**: http://127.0.0.1:8000/products/
- **مقارنة المنتجات**: http://127.0.0.1:8000/compare/
- **لوحة تحكم المستخدم**: http://127.0.0.1:8000/dashboard/

### واجهات الإدارة:
- **لوحة تحكم الإدارة**: http://127.0.0.1:8000/admin/
- **لوحة تحكم المتجر**: http://127.0.0.1:8000/store/dashboard/
- **API التوثيق**: http://127.0.0.1:8000/api/docs/

### واجهات برمجة التطبيقات (APIs):
- **API الواجهة الأمامية**: http://127.0.0.1:8000/api/frontend/
- **API تكامل المتاجر**: http://127.0.0.1:8000/api/store-integration/
- **API الاستكشاف**: http://127.0.0.1:8000/api/discovery/
- **API المراجعات**: http://127.0.0.1:8000/api/reviews/

## الميزات المتاحة

### للزوار:
- ✅ البحث الذكي عن المنتجات مع الإكمال التلقائي
- ✅ مقارنة المنتجات جنباً إلى جنب
- ✅ عرض تفاصيل المنتجات مع مقارنة الأسعار
- ✅ قراءة المراجعات مع تحليل المشاعر
- ✅ استعراض المتاجر والعلامات التجارية

### للمستخدمين المسجلين:
- ✅ لوحة تحكم شخصية
- ✅ كتابة وإدارة المراجعات
- ✅ حفظ المنتجات المفضلة
- ✅ تتبع النشاط والإحصائيات
- ✅ توصيات مخصصة بالذكاء الاصطناعي

### لأصحاب المتاجر:
- ✅ لوحة تحكم المتجر
- ✅ إدارة المنتجات والمخزون
- ✅ مراقبة الأداء والتقييمات
- ✅ تكامل مع منصات التجارة الإلكترونية
- ✅ تحليلات مفصلة للمبيعات

### للمديرين:
- ✅ لوحة تحكم إدارية شاملة
- ✅ إدارة المتاجر والمستخدمين
- ✅ مراقبة النظام والأداء
- ✅ إدارة المراجعات والمحتوى
- ✅ تقارير وتحليلات متقدمة

## حل المشاكل الشائعة

### مشكلة: Python غير موجود
```bash
# تحقق من التثبيت
python --version
python3 --version

# إذا لم يكن مثبتاً، قم بتثبيته من python.org
```

### مشكلة: pip غير موجود
```bash
# Windows
python -m ensurepip --upgrade

# macOS/Linux
sudo apt install python3-pip  # Ubuntu/Debian
brew install python3          # macOS
```

### مشكلة: خطأ في المتطلبات
```bash
# قم بتحديث pip أولاً
pip install --upgrade pip

# ثم ثبت المتطلبات
pip install -r requirements.txt
```

### مشكلة: خطأ في قاعدة البيانات
```bash
# احذف قاعدة البيانات وأعد إنشاءها
rm db.sqlite3
python manage.py makemigrations
python manage.py migrate
```

### مشكلة: المنفذ مستخدم
```bash
# استخدم منفذ مختلف
python manage.py runserver 8001
```

## البيانات التجريبية

لتجربة المنصة بشكل كامل، يمكنك:

1. **إنشاء مستخدم إداري**:
```bash
python manage.py createsuperuser
```

2. **إضافة بيانات تجريبية** عبر لوحة الإدارة:
   - متاجر وهمية
   - منتجات تجريبية
   - مراجعات عينة

## الدعم والمساعدة

إذا واجهت أي مشاكل:

1. **تحقق من السجلات** في terminal/command prompt
2. **راجع ملف requirements.txt** للتأكد من المتطلبات
3. **تأكد من إصدار Python** (يجب أن يكون 3.8+)
4. **تحقق من اتصال الإنترنت** لتحميل المتطلبات

## ملاحظات مهمة

- 🔒 **الأمان**: هذا إعداد للتطوير فقط، لا تستخدمه في الإنتاج
- 🌐 **الشبكة**: الخادم يعمل على جميع واجهات الشبكة (0.0.0.0)
- 📱 **الاستجابة**: الواجهات مصممة للعمل على جميع الأجهزة
- 🚀 **الأداء**: قد تحتاج لتحسينات إضافية للاستخدام المكثف

---

**استمتع باستخدام منصة التجارة الإلكترونية الذكية! 🛍️✨**
