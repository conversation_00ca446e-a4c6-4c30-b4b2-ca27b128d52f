# Generated by Django 5.1.6 on 2025-04-09 14:11

import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Brand',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='Brand Name')),
                ('popularity', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='Popularity Score')),
                ('rating', models.DecimalField(decimal_places=2, default=0, max_digits=3, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(5)], verbose_name='Brand Rating')),
                ('likes', models.PositiveIntegerField(default=0, verbose_name='Likes')),
                ('dislikes', models.PositiveIntegerField(default=0, verbose_name='Dislikes')),
            ],
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, null=True, unique=True, verbose_name='Category Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
            ],
        ),
        migrations.CreateModel(
            name='Owner',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='Email')),
                ('password', models.CharField(max_length=255, verbose_name='Password')),
                ('last_login_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Last Login Date')),
            ],
        ),
        migrations.CreateModel(
            name='SpecificationCategory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='المعرف الفريد لتصنيف المواصفات', primary_key=True, serialize=False)),
                ('category_name', models.CharField(help_text='اسم تصنيف المواصفات', max_length=255, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='Shop',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='Shop Name')),
                ('address', models.CharField(max_length=500, verbose_name='Shop Address')),
                ('logo', models.ImageField(upload_to='shop_logos/', verbose_name='Shop Logo')),
                ('url', models.URLField(verbose_name='Shop URL')),
                ('owner', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='shop', to='core.owner')),
            ],
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=4000, unique=True, verbose_name='Product Name')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='Price (USD)')),
                ('release_date', models.DateField(blank=True, null=True, verbose_name='Release Date')),
                ('last_price_update', models.DateField(blank=True, null=True, verbose_name='Last Price Update')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('image_url', models.URLField(blank=True, null=True, verbose_name='Image URL')),
                ('video_url', models.URLField(blank=True, null=True, verbose_name='Video URL')),
                ('rating', models.DecimalField(decimal_places=2, max_digits=3, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(5)], verbose_name='Product Rating')),
                ('likes', models.PositiveIntegerField(default=0, verbose_name='Likes')),
                ('dislikes', models.PositiveIntegerField(default=0, verbose_name='Dislikes')),
                ('neutrals', models.PositiveIntegerField(default=0, verbose_name='Neutrals')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('brand', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='core.brand', verbose_name='Brand')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='core.category', verbose_name='Category')),
                ('shop', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='core.shop')),
            ],
        ),
        migrations.CreateModel(
            name='Specification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='المعرف الفريد للمواصفة', primary_key=True, serialize=False)),
                ('specification_name', models.CharField(help_text='اسم المواصفة', max_length=255, unique=True)),
                ('category', models.ForeignKey(help_text='تصنيف المواصفة', on_delete=django.db.models.deletion.CASCADE, to='core.specificationcategory')),
            ],
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('user_type', models.CharField(choices=[('admin', 'Admin'), ('owner', 'Owner'), ('customer', 'Customer')], default='customer', max_length=10)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to.', related_name='core_user_set', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='core_user_set', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.AddField(
            model_name='owner',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='owner_profile', to='core.user'),
        ),
        migrations.CreateModel(
            name='ProductSpecification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='المعرف الفريد لربط المنتج بالمواصفة', primary_key=True, serialize=False)),
                ('specification_value', models.CharField(help_text='قيمة المواصفة للمنتج', max_length=255)),
                ('product', models.ForeignKey(help_text='المنتج المرتبط بالمواصفة', on_delete=django.db.models.deletion.CASCADE, to='core.product')),
                ('specification', models.ForeignKey(help_text='المواصفة المرتبطة بالمنتج', on_delete=django.db.models.deletion.CASCADE, to='core.specification')),
            ],
            options={
                'verbose_name': 'Product Specification',
                'verbose_name_plural': 'Product Specification',
                'unique_together': {('product', 'specification')},
            },
        ),
    ]
