# Generated by Django 5.2 on 2025-04-16 04:33

import django.core.validators
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0010_add_notification_model'),
    ]

    operations = [
        migrations.AlterField(
            model_name='brand',
            name='rating',
            field=models.DecimalField(decimal_places=2, max_digits=3, validators=[django.core.validators.MinValueValidator(Decimal('0')), django.core.validators.MaxValueValidator(Decimal('5'))], verbose_name='Brand Rating'),
        ),
    ]
