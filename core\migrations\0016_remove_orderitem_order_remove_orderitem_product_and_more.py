# Generated by Django 5.1 on 2025-04-28 07:20

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0015_merge_20250421_1310'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='orderitem',
            name='order',
        ),
        migrations.RemoveField(
            model_name='orderitem',
            name='product',
        ),
        migrations.AddField(
            model_name='user',
            name='is_email_verified',
            field=models.BooleanField(default=False, help_text="Indicates whether the user's email has been verified.", verbose_name='Is Email Verified'),
        ),
        migrations.CreateModel(
            name='ActionVerificationToken',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.UUIDField(default=uuid.uuid4, editable=False, help_text='The verification token.', unique=True)),
                ('action_type', models.CharField(choices=[('delete_product', 'Delete Product'), ('bulk_delete', 'Bulk Delete'), ('update_stock', 'Update Stock'), ('change_password', 'Change Password')], max_length=20, verbose_name='Action Type')),
                ('object_id', models.CharField(blank=True, help_text='ID of the object being acted upon.', max_length=255, null=True, verbose_name='Object ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('expires_at', models.DateTimeField(verbose_name='Expires At')),
                ('is_used', models.BooleanField(default=False, help_text='Indicates whether the token has been used.', verbose_name='Is Used')),
                ('user', models.ForeignKey(help_text='The user who initiated the action.', on_delete=django.db.models.deletion.CASCADE, related_name='action_tokens', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='EmailVerificationToken',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.UUIDField(default=uuid.uuid4, editable=False, help_text='The verification token.', unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('expires_at', models.DateTimeField(verbose_name='Expires At')),
                ('is_used', models.BooleanField(default=False, help_text='Indicates whether the token has been used.', verbose_name='Is Used')),
                ('user', models.OneToOneField(help_text='The user who needs to verify their email.', on_delete=django.db.models.deletion.CASCADE, related_name='verification_token', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Favorite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('product', models.ForeignKey(help_text='The product that was favorited.', on_delete=django.db.models.deletion.CASCADE, related_name='favorited_by', to='core.product')),
                ('user', models.ForeignKey(help_text='The user who favorited the product.', on_delete=django.db.models.deletion.CASCADE, related_name='favorites', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Favorite',
                'verbose_name_plural': 'Favorites',
                'ordering': ['-created_at'],
                'unique_together': {('user', 'product')},
            },
        ),
        migrations.CreateModel(
            name='UserPreference',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('min_price', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Minimum Price')),
                ('max_price', models.DecimalField(decimal_places=2, default=10000, max_digits=10, verbose_name='Maximum Price')),
                ('user', models.OneToOneField(help_text='The user who owns these preferences.', on_delete=django.db.models.deletion.CASCADE, related_name='preferences', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='BrandPreference',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('brand', models.ForeignKey(help_text='The brand that is preferred.', on_delete=django.db.models.deletion.CASCADE, related_name='preferred_by', to='core.brand')),
                ('user_preference', models.ForeignKey(help_text='The user preference this brand preference belongs to.', on_delete=django.db.models.deletion.CASCADE, related_name='brand_preferences', to='core.userpreference')),
            ],
            options={
                'verbose_name': 'Brand Preference',
                'verbose_name_plural': 'Brand Preferences',
                'ordering': ['brand__name'],
                'unique_together': {('user_preference', 'brand')},
            },
        ),
        migrations.DeleteModel(
            name='Order',
        ),
        migrations.DeleteModel(
            name='OrderItem',
        ),
    ]
