# Generated by Django 5.1 on 2025-06-22 13:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0019_user_phone'),
    ]

    operations = [
        migrations.AlterField(
            model_name='user',
            name='email',
            field=models.EmailField(help_text='البريد الإلكتروني للمستخدم. يجب أن يكون فريدًا.', max_length=254, unique=True, verbose_name='Email Address'),
        ),
        migrations.AlterField(
            model_name='user',
            name='phone',
            field=models.CharField(blank=True, help_text='رقم الجوال للمستخدم (اختياري).', max_length=20, null=True, verbose_name='Phone Number'),
        ),
        migrations.AlterField(
            model_name='user',
            name='user_type',
            field=models.CharField(choices=[('admin', 'Admin'), ('owner', 'Owner'), ('customer', 'Customer')], default='customer', help_text='نوع المستخدم: مدير، مالك، أو عميل.', max_length=10, verbose_name='User Type'),
        ),
    ]
