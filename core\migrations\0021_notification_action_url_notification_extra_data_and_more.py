# Generated by Django 5.1 on 2025-06-22 13:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0020_alter_user_email_alter_user_phone_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='notification',
            name='action_url',
            field=models.URLField(blank=True, null=True, verbose_name='Action URL'),
        ),
        migrations.AddField(
            model_name='notification',
            name='extra_data',
            field=models.JSONField(blank=True, default=dict, null=True, verbose_name='Extra Data'),
        ),
        migrations.AlterField(
            model_name='notification',
            name='notification_type',
            field=models.CharField(choices=[('promotion', 'Promotion'), ('order', 'Order Update'), ('general', 'General'), ('inventory', 'Inventory Alert'), ('product_rating', 'Product Rating'), ('shop_status', 'Shop Status')], default='general', max_length=20),
        ),
        migrations.DeleteModel(
            name='UserProductReaction',
        ),
    ]
