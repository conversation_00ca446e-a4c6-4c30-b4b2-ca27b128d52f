# Generated by Django 5.1 on 2025-06-24 06:57

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0021_notification_action_url_notification_extra_data_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserProductReaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reaction_type', models.CharField(choices=[('like', 'إعجاب'), ('dislike', 'عدم إعجاب'), ('neutral', 'محايد'), ('view', 'مشاهدة')], default='neutral', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_reactions', to='core.product')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_reactions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'تفاعل مستخدم مع منتج',
                'verbose_name_plural': 'تفاعلات المستخدمين مع المنتجات',
                'unique_together': {('user', 'product', 'reaction_type')},
            },
        ),
    ]
