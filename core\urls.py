"""
core/urls.py
--------------
Defines authentication and admin-related API endpoints for the core app.
"""

from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView, TokenObtainPairView
from rest_framework.routers import DefaultRouter

from .views import (
    RegisterAPIView,
    LoginAPIView,
    LogoutAPIView,
    AccessPointLoginAPIView,
    UserViewSet,
    UserProfileAPIView,
    CreateOwnerProfileView,
    ShopCheckView,
)
from .views_site_admin import ensure_site_view
from .views_ai_rating import AIRatingViewSet

app_name = 'core'

router = DefaultRouter()
router.register(r'users', UserViewSet, basename='user')
router.register(r'ai-ratings', AIRatingViewSet, basename='ai-rating')

urlpatterns = [
    path('register/', RegisterAPIView.as_view(), name='register'),
    path('login/', LoginAPIView.as_view(), name='login'),
    path('logout/', LogoutAPIView.as_view(), name='logout'),
    path('token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('token/verify/', TokenObtainPairView.as_view(), name='token_verify'),
    path('ensure-site/', ensure_site_view, name='ensure-site'),
    path('profile/', UserProfileAPIView.as_view(), name='user-profile'),
    path('owner/profile/create/', CreateOwnerProfileView.as_view(), name='create-owner-profile'),
    path('shop/check/', ShopCheckView.as_view(), name='shop-check'),
]

urlpatterns += router.urls
