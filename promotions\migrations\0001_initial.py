# Generated by Django 5.1.6 on 2025-04-14 17:01

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0008_add_is_banned'),
    ]

    operations = [
        migrations.CreateModel(
            name='Promotion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='Promotion Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Start Date')),
                ('end_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='End Date')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
            ],
        ),
        migrations.CreateModel(
            name='DiscountCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='Discount Code')),
                ('discount_percentage', models.DecimalField(decimal_places=2, max_digits=5, verbose_name='Discount Percentage')),
                ('max_uses', models.PositiveIntegerField(default=1, verbose_name='Max Uses')),
                ('used_count', models.PositiveIntegerField(default=0, verbose_name='Used Count')),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='discount_codes', to='core.product')),
                ('promotion', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='discount_codes', to='promotions.promotion')),
            ],
        ),
    ]
