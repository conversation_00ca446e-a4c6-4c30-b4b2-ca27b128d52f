/* Best in Click - Main Stylesheet */

/* Root Variables */
:root {
  --primary-color: #0d6efd;
  --primary-dark: #0056b3;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --white: #ffffff;
  --black: #000000;
  
  --font-family-sans-serif: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-size-base: 1rem;
  --line-height-base: 1.5;
  
  --border-radius: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 1rem;
  
  --box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  
  --transition-base: all 0.3s ease;
}

/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-sans-serif);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--dark-color);
  background-color: var(--light-color);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5rem;
}

.display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
  font-weight: 700;
  line-height: 1.1;
}

/* Links */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-base);
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Buttons */
.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: var(--transition-base);
  border: 1px solid transparent;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--box-shadow);
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, #004085 100%);
  border-color: var(--primary-dark);
}

/* Cards */
.card {
  border: none;
  border-radius: var(--border-radius-xl);
  box-shadow: var(--box-shadow-sm);
  transition: var(--transition-base);
}

.card:hover {
  box-shadow: var(--box-shadow);
  transform: translateY(-2px);
}

.card-header {
  background: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  font-weight: 600;
}

/* Navigation */
.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
}

.navbar-nav .nav-link {
  font-weight: 500;
  transition: var(--transition-base);
}

.navbar-nav .nav-link:hover {
  color: var(--primary-color) !important;
}

/* Forms */
.form-control, .form-select {
  border-radius: var(--border-radius);
  border: 1px solid #ced4da;
  transition: var(--transition-base);
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Alerts */
.alert {
  border-radius: var(--border-radius-lg);
  border: none;
}

/* Badges */
.badge {
  font-weight: 500;
  border-radius: var(--border-radius);
}

/* Utilities */
.text-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
}

.shadow-custom {
  box-shadow: var(--box-shadow);
}

.shadow-custom-lg {
  box-shadow: var(--box-shadow-lg);
}

.rounded-custom {
  border-radius: var(--border-radius-xl);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in {
  animation: slideIn 0.6s ease-out;
}

/* Loading States */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .display-1 { font-size: 2.5rem; }
  .display-2 { font-size: 2rem; }
  .display-3 { font-size: 1.75rem; }
  .display-4 { font-size: 1.5rem; }
  .display-5 { font-size: 1.25rem; }
  .display-6 { font-size: 1rem; }
  
  .btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
  
  .card {
    margin-bottom: 1rem;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --light-color: #212529;
    --dark-color: #f8f9fa;
  }
  
  body {
    background-color: var(--dark-color);
    color: var(--light-color);
  }
  
  .card {
    background-color: #343a40;
    color: var(--light-color);
  }
}

/* Print Styles */
@media print {
  .btn, .navbar, .alert {
    display: none !important;
  }
  
  .card {
    box-shadow: none;
    border: 1px solid #dee2e6;
  }
  
  a {
    color: var(--dark-color) !important;
    text-decoration: none !important;
  }
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for better accessibility */
.btn:focus,
.form-control:focus,
.form-select:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn {
    border-width: 2px;
  }
  
  .card {
    border: 2px solid var(--dark-color);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
