<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل المنتج</title>
    <meta name="description" content="تفاصيل المنتج: استعرض معلومات المنتج، حالته، وإرسال إشعارات للمالك من لوحة التحكم.">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        .navbar {
            background: linear-gradient(90deg, #1976d2 0%, #0d47a1 100%);
            color: #fff;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 68px;
            box-shadow: 0 4px 18px rgba(30,60,120,0.13);
            border-radius: 0 0 18px 18px;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            z-index: 100;
        }
        body { padding-top: 80px !important; }
        .navbar .brand { font-size: 1.6em; font-weight: bold; padding: 0 40px; display: flex; align-items: center; gap: 12px; letter-spacing: 1px; }
        .navbar .brand .icon { font-size: 2em; margin-left: 8px; filter: drop-shadow(0 2px 4px #0d47a1aa); }
        .navbar .menu { display: flex; align-items: center; gap: 12px; padding: 0 32px; }
        .navbar .menu button { background: rgba(255,255,255,0.13); color: #fff; border: none; border-radius: 50%; width: 44px; height: 44px; display: flex; align-items: center; justify-content: center; font-size: 1.5em; cursor: pointer; transition: background 0.2s, transform 0.2s; box-shadow: 0 2px 8px rgba(30,60,120,0.10); }
        .navbar .menu button:hover { background: #fff; color: #1976d2; transform: translateY(-2px) scale(1.08); }
        @media (max-width: 700px) { .navbar .brand { font-size: 1.1em; padding: 0 10px; } .navbar .menu { padding: 0 8px; } }
        .container { max-width: 700px; margin: 40px auto; background: #fff; border-radius: 18px; box-shadow: 0 6px 32px rgba(30,60,120,0.10); padding: 36px 28px; }
        h2 { color: #0d47a1; text-align: center; }
        .row { display: flex; gap: 24px; margin-bottom: 18px; }
        .col { flex: 1; }
        .label { color: #1976d2; font-weight: bold; }
        .value { color: #263238; }
        .actions { display: flex; gap: 12px; justify-content: flex-end; margin-top: 24px; }
        .actions button { background: #1976d2; color: #fff; border: none; border-radius: 6px; padding: 8px 20px; font-size: 1em; cursor: pointer; }
        .actions button.deactivate { background: #b71c1c; }
        .actions button.notify { background: #ff9800; }
        .actions button:disabled { opacity: 0.6; cursor: not-allowed; }
        .notify-form { margin-top: 18px; display: none; }
        .notify-form textarea { width: 100%; min-height: 60px; border-radius: 6px; border: 1px solid #bdbdbd; padding: 8px; font-size: 1em; }
        .notify-form button { margin-top: 8px; }
        .success { color: #388e3c; text-align: center; margin-bottom: 12px; }
        .error { color: #b71c1c; text-align: center; margin-bottom: 12px; }
    </style>
</head>
<body>
    <div class="navbar">
        <span class="brand"><span class="icon">📦</span> تفاصيل المنتج</span>
        <div class="menu">
            <button title="رجوع" onclick="window.history.back()"><span>🔙</span></button>
            <button title="الرئيسية" onclick="window.location.href='/apps-dashboard/'"><span>🏠</span></button>
            <button title="خروج" onclick="logout()" style="background:#b71c1c;"><span>🔓</span></button>
        </div>
    </div>
    <div class="container">
        <h2>تفاصيل المنتج</h2>
        <div id="productDetails"></div>
        <div class="actions">
            <button id="activateBtn"></button>
            <button class="notify" onclick="showNotifyForm()">إرسال إشعار للمالك</button>
        </div>
        <form class="notify-form" id="notifyForm" onsubmit="return sendNotify(event)">
            <textarea id="notifyMsg" placeholder="اكتب رسالة الإشعار..."></textarea>
            <button type="submit">إرسال</button>
        </form>
        <div id="msg"></div>
    </div>
    <script>
        const params = new URLSearchParams(window.location.search);
        const productId = params.get('id');
        let product = null;
        let productStats = null;
        console.log('Product ID from URL:', productId);
        if (!productId) {
            document.getElementById('productDetails').innerHTML = '<div class="error">لم يتم تحديد المنتج بشكل صحيح!</div>';
        } else {
            async function fetchProduct(retry = true) {
                const token = localStorage.getItem('access_token');
                const res = await fetch(`/api/products/${productId}/`, {
                    headers: token ? { 'Authorization': 'Bearer ' + token } : {}
                });
                if (res.status === 401 && retry) {
                    // حاول تحديث التوكن
                    const refresh = localStorage.getItem('refresh_token');
                    if (refresh) {
                        const refreshRes = await fetch('/api/auth/token/refresh/', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ refresh })
                        });
                        const refreshData = await refreshRes.json();
                        if (refreshRes.ok && refreshData.access) {
                            localStorage.setItem('access_token', refreshData.access);
                            return fetchProduct(false); // أعد المحاولة مرة واحدة فقط
                        } else {
                            alert('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مجددًا.');
                            window.location.href = '/static/login.html';
                            return;
                        }
                    } else {
                        alert('يرجى تسجيل الدخول أولاً.');
                        window.location.href = '/static/login.html';
                        return;
                    }
                }
                const data = await res.json();
                product = data;
                await fetchProductStats();
                renderProduct();
            }
            async function fetchProductStats() {
                const token = localStorage.getItem('access_token');
                const res = await fetch(`/api/products/${productId}/reviews-stats/`, {
                    headers: token ? { 'Authorization': 'Bearer ' + token } : {}
                });
                productStats = await res.json();
                console.log('إحصائيات المنتج:', productStats);
            }
            fetchProduct();
        }
        function renderProduct() {
            const d = product;
            let html = `<div class='row'><div class='col'><span class='label'>الاسم:</span> <span class='value'>${d.name}</span></div><div class='col'><span class='label'>السعر:</span> <span class='value'>${d.price}</span></div></div>`;
            html += `<div class='row'><div class='col'><span class='label'>الفئة:</span> <span class='value'>${d.category ? d.category.name : ''}</span></div><div class='col'><span class='label'>البراند:</span> <span class='value'>${d.brand ? d.brand.name : '-'}</span></div></div>`;
            html += `<div class='row'><div class='col'><span class='label'>المتجر:</span> <span class='value'>${d.shop && d.shop.id ? `<a href='/static/shop_detail.html?id=${d.shop.id}' style='color:#1976d2;text-decoration:underline;cursor:pointer;'>${d.shop.name}</a>` : (d.shop ? d.shop.name : '-')}</span></div><div class='col'><span class='label'>مالك المتجر:</span> <span class='value'>${d.shop && d.shop.owner_id ? `<a href='/static/user_profile.html?id=${d.shop.owner_id}' style='color:#1976d2;text-decoration:underline;cursor:pointer;'>${d.shop.owner_username}</a>` : (d.shop && d.shop.owner_username ? d.shop.owner_username : '-')}</span></div></div>`;
            html += `<div class='row'><div class='col'><span class='label'>الحالة:</span> <span class='value' id='statusValue'>${d.is_active ? 'نشط' : 'غير نشط'}</span></div><div class='col'><span class='label'>تاريخ الإضافة:</span> <span class='value'>${d.created_at ? d.created_at.split('T')[0] : '-'}</span></div></div>`;
            html += `<div class='row'><div class='col'><span class='label'>الوصف:</span> <span class='value'>${d.description || '-'}</span></div></div>`;
            if(productStats) {
                html += `<div class='row'><div class='col'><span class='label'>عدد الإعجابات:</span> <span class='value'>${productStats.likes}</span></div><div class='col'><span class='label'>عدد عدم الإعجاب:</span> <span class='value'>${productStats.dislikes}</span></div></div>`;
                html += `<div class='row'><div class='col'><span class='label'>عدد المحايدين:</span> <span class='value'>${productStats.neutrals}</span></div><div class='col'><span class='label'>عدد المشاهدات:</span> <span class='value'>${productStats.views}</span></div></div>`;
                html += `<div class='row'><div class='col'><span class='label'>عدد المستخدمين المعجبين:</span> <span class='value'>${productStats.users_liked}</span></div><div class='col'><span class='label'>التقييم:</span> <span class='value'>${productStats.rating}</span></div></div>`;
                html += `<div class='row'><div class='col'><span class='label'>عدد المراجعات:</span> <span class='value'>${productStats.reviews_count}</span></div></div>`;
                html += `<div class='row'><div class='col'><span class='label'>جميع المراجعات:</span></div></div>`;
                if(productStats.reviews && productStats.reviews.length > 0) {
                    html += `<ul style='background:#f7fafd;padding:10px 18px;border-radius:8px;'>`;
                    productStats.reviews.forEach(r => {
                        html += `<li><b>${r.user.name}:</b> ${r.comment || ''} <span style='color:#ffb300'>★${r.rating}</span></li>`;
                    });
                    html += `</ul>`;
                } else {
                    html += `<div style='color:#888'>لا توجد مراجعات بعد.</div>`;
                }
            }
            document.getElementById('productDetails').innerHTML = html;
            document.getElementById('activateBtn').textContent = d.is_active ? 'تعطيل المنتج' : 'تنشيط المنتج';
            document.getElementById('activateBtn').className = d.is_active ? 'deactivate' : '';
        }
        document.getElementById('activateBtn').onclick = async function() {
            const activate = !product.is_active;
            const token = localStorage.getItem('access_token');
            const res = await fetch(`/api/products/${product.id}/update/`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + token
                },
                body: JSON.stringify({ is_active: activate })
            });
            if(res.ok) {
                product.is_active = activate;
                document.getElementById('statusValue').textContent = activate ? 'نشط' : 'غير نشط';
                renderProduct();
                showMsg('تم تحديث حالة المنتج بنجاح', true);
            } else {
                showMsg('حدث خطأ أثناء تحديث حالة المنتج', false);
            }
        };
        function showNotifyForm() {
            document.getElementById('notifyForm').style.display = 'block';
        }
        async function sendNotify(e) {
            e.preventDefault();
            const msg = document.getElementById('notifyMsg').value.trim();
            if(!msg) return showMsg('يرجى كتابة رسالة', false);
            const res = await fetch(`/api/dashboard/admin/products/${product.id}/notify/`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message: msg })
            });
            if(res.ok) {
                showMsg('تم إرسال الإشعار للمالك بنجاح', true);
                document.getElementById('notifyForm').reset();
                document.getElementById('notifyForm').style.display = 'none';
            } else {
                showMsg('حدث خطأ أثناء إرسال الإشعار', false);
            }
        }
        function showMsg(txt, success) {
            document.getElementById('msg').innerHTML = `<div class='${success ? 'success' : 'error'}'>${txt}</div>`;
            setTimeout(()=>{ document.getElementById('msg').innerHTML = '' }, 3000);
        }
    </script>
</body>
</html>
