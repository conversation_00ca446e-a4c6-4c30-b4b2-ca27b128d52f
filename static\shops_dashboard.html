<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المتاجر</title>
    <meta name="description" content="إدارة المتاجر: راقب وأدر المتاجر، مالكيها، ومنتجاتهم بسهولة من لوحة التحكم الحديثة.">
    <link rel="stylesheet" href="/static/dashboard_style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        .crud-table {
            width: 100%;
            border-collapse: collapse;
            background: #fff;
            border-radius: 14px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(30,60,120,0.07);
            font-size: 1.08em;
        }
        .crud-table thead {
            background: linear-gradient(90deg, #1976d2 0%, #0d47a1 100%);
            color: #fff;
        }
        .crud-table th, .crud-table td {
            padding: 16px 10px;
            text-align: center;
        }
        .crud-table th {
            font-size: 1.1em;
            letter-spacing: 1px;
            border-bottom: 2px solid #1976d2;
        }
        .crud-table tbody tr {
            transition: background 0.18s;
        }
        .crud-table tbody tr:nth-child(even) {
            background: #f4f7fb;
        }
        .crud-table tbody tr:nth-child(odd) {
            background: #fafdff;
        }
        .crud-table tbody tr:hover {
            background: #e3e7ee;
            box-shadow: 0 2px 8px #1976d22a;
        }
        .crud-table td {
            border-bottom: 1px solid #e3e7ee;
        }
        .navbar {
            background: linear-gradient(90deg, #1976d2 0%, #0d47a1 100%);
            color: #fff;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 68px;
            box-shadow: 0 4px 18px rgba(30,60,120,0.13);
            border-radius: 0 0 18px 18px;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            z-index: 100;
        }
        body { padding-top: 80px !important; }
        .navbar .brand {
            font-size: 1.6em;
            font-weight: bold;
            padding: 0 40px;
            display: flex;
            align-items: center;
            gap: 12px;
            letter-spacing: 1px;
        }
        .navbar .brand .icon {
            font-size: 2em;
            margin-left: 8px;
            filter: drop-shadow(0 2px 4px #0d47a1aa);
        }
        .navbar .menu {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 0 32px;
        }
        .navbar .menu button {
            background: rgba(255,255,255,0.13);
            color: #fff;
            border: none;
            border-radius: 50%;
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            cursor: pointer;
            transition: background 0.2s, transform 0.2s;
            box-shadow: 0 2px 8px rgba(30,60,120,0.10);
        }
        .navbar .menu button:hover {
            background: #fff;
            color: #1976d2;
            transform: translateY(-2px) scale(1.08);
        }
        @media (max-width: 700px) {
            .crud-table th, .crud-table td { padding: 10px 4px; font-size: 0.98em; }
            .navbar .brand { font-size: 1.1em; padding: 0 10px; }
            .navbar .menu { padding: 0 8px; }
        }
    </style>
</head>
<body>
    <div class="navbar">
        <span class="brand"><span class="icon">🏪</span> إدارة المتاجر</span>
        <div class="menu">
            <button title="الرئيسية" onclick="window.location.href='/apps-dashboard/'"><span>🏠</span></button>
            <button title="خروج" onclick="logout()" style="background:#b71c1c;"><span>🔓</span></button>
        </div>
    </div>
    <div class="dashboard">
        <h1><span style="font-size:1.2em;vertical-align:middle;">🏪</span> إدارة المتاجر <span style="font-size:0.7em;color:#1976d2;"></span></h1>
        <div class="actions">
            <button class="add-btn"><span style="font-size:1.1em;">➕</span> إضافة متجر جديد</button>
        </div>
        <div style="width:100%;overflow-x:auto;">
            <table class="crud-table" id="shopsTable" style="width:100%;min-width:700px;">
                <thead>
                    <tr>
                        <th>🖼️</th>
                        <th>🏪</th>
                        <th>📧</th>
                        <th>⚙️</th>
                    </tr>
                </thead>
                <tbody id="shops-table-body">
                    <!-- الصفوف ستملأ ديناميكياً -->
                </tbody>
            </table>
        </div>
    </div>
    <script src="/static/dashboard_protect.js"></script>
    <script>
    async function fetchShops(retry = true) {
        const token = localStorage.getItem('access_token');
        const tbody = document.getElementById('shops-table-body');
        tbody.innerHTML = '<tr><td colspan="5">جاري التحميل...</td></tr>';
        try {
            const res = await fetch('/api/shop/', {
                headers: token ? { 'Authorization': 'Bearer ' + token } : {}
            });
            if (res.status === 401 && retry) {
                // حاول تحديث التوكن
                const refresh = localStorage.getItem('refresh_token');
                if (refresh) {
                    const refreshRes = await fetch('/api/auth/token/refresh/', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ refresh })
                    });
                    const refreshData = await refreshRes.json();
                    if (refreshRes.ok && refreshData.access) {
                        localStorage.setItem('access_token', refreshData.access);
                        return fetchShops(false); // أعد المحاولة مرة واحدة فقط
                    } else {
                        alert('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مجددًا.');
                        window.location.href = '/static/login.html';
                        return;
                    }
                } else {
                    alert('يرجى تسجيل الدخول أولاً.');
                    window.location.href = '/static/login.html';
                    return;
                }
            }
            if (!res.ok) throw new Error('فشل في جلب المتاجر');
            const shops = await res.json();
            if (!Array.isArray(shops) || shops.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5">لا توجد متاجر</td></tr>';
                return;
            }
            tbody.innerHTML = '';
            for (const [i, shop] of shops.entries()) {
                tbody.innerHTML += `
                <tr onclick="window.location.href='/static/shop_detail.html?id=${shop.id}'" style="cursor:pointer;">
                    <td><img src="${shop.logo || '/media/shop_logos/default.png'}" width="40" onerror="this.onerror=null;this.src='/media/shop_logos/default.png';"></td>
                    <td>${shop.name}</td>
                    <td>${shop.email || ''}</td>
                    <td>${shop.is_active ? 'نشط' : 'غير نشط'}</td>
                </tr>`;
            }
        } catch (e) {
            tbody.innerHTML = '<tr><td colspan="5">حدث خطأ في جلب البيانات</td></tr>';
        }
    }

    async function toggleShopActive(shopId, isActive) {
        const token = localStorage.getItem('access_token');
        try {
            const res = await fetch(`/api/shop/${shopId}/`, {
                method: 'PATCH',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ is_active: !isActive })
            });
            if (!res.ok) throw new Error('فشل في تحديث حالة المتجر');
            fetchShops();
        } catch (e) {
            alert('حدث خطأ أثناء تحديث حالة المتجر!');
        }
    }

    async function deleteShop(shopId) {
        const token = localStorage.getItem('access_token');
        if(!confirm('هل أنت متأكد من حذف المتجر؟')) return;
        try {
            const res = await fetch(`/api/shop/${shopId}/`, {
                method: 'DELETE',
                headers: { 'Authorization': 'Bearer ' + token }
            });
            if (!res.ok) throw new Error('فشل في حذف المتجر');
            fetchShops();
        } catch (e) {
            alert('حدث خطأ أثناء حذف المتجر!');
        }
    }

    fetchShops();
    </script>
</body>
</html>
