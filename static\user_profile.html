<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بروفايل المستخدم</title>
    <meta name="description" content="صفحة بروفايل المستخدم: استعرض بيانات المستخدم وصلاحياته وإحصائياته بشكل احترافي.">
    <link rel="stylesheet" href="/static/dashboard_style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        .navbar { background: linear-gradient(90deg, #1976d2 0%, #0d47a1 100%); color: #fff; padding: 0; display: flex; align-items: center; justify-content: space-between; height: 68px; box-shadow: 0 4px 18px rgba(30,60,120,0.13); border-radius: 0 0 18px 18px; position: fixed; top: 0; left: 0; width: 100vw; z-index: 100; }
        body { padding-top: 80px !important; background: #f6f8fa; }
        .navbar .brand { font-size: 1.6em; font-weight: bold; padding: 0 40px; display: flex; align-items: center; gap: 12px; letter-spacing: 1px; }
        .navbar .brand .icon { font-size: 2em; margin-left: 8px; filter: drop-shadow(0 2px 4px #0d47a1aa); }
        .navbar .menu { display: flex; align-items: center; gap: 12px; padding: 0 32px; }
        .navbar .menu button { background: rgba(255,255,255,0.13); color: #fff; border: none; border-radius: 50%; width: 44px; height: 44px; display: flex; align-items: center; justify-content: center; font-size: 1.5em; cursor: pointer; transition: background 0.2s, transform 0.2s; box-shadow: 0 2px 8px rgba(30,60,120,0.10); }
        .navbar .menu button:hover { background: #fff; color: #1976d2; transform: translateY(-2px) scale(1.08); }
        @media (max-width: 700px) { .navbar .brand { font-size: 1.1em; padding: 0 10px; } .navbar .menu { padding: 0 8px; } }
        .profile-container { max-width: 700px; margin: 40px auto; background: #fff; border-radius: 18px; box-shadow: 0 6px 32px rgba(30,60,120,0.10); padding: 36px 28px; }
        .profile-header { display: flex; align-items: center; gap: 32px; margin-bottom: 24px; }
        .profile-header .avatar { width: 110px; height: 110px; border-radius: 50%; object-fit: cover; background: #e3e7ee; }
        .profile-info { flex: 1; }
        .profile-info h2 { color: #0d47a1; margin: 0 0 8px 0; }
        .profile-info .role { display: inline-block; padding: 4px 16px; border-radius: 8px; font-size: 1em; background: #e3f7e3; color: #388e3c; margin-right: 8px; }
        .profile-info .role.admin { background: #ffeaea; color: #b71c1c; }
        .profile-details-row { margin-bottom: 8px; color: #374151; }
        .section-title { color: #1976d2; font-size: 1.2em; margin: 32px 0 12px 0; border-bottom: 1px solid #e3e7ee; padding-bottom: 4px; }
        .stat-card { background: #f4f7fb; border-radius: 12px; padding: 18px 24px; min-width: 120px; text-align: center; box-shadow: 0 2px 8px rgba(30,60,120,0.07); margin-bottom: 8px; display: inline-block; margin-left: 8px; }
        .stat-label { color: #1976d2; font-size: 1em; margin-bottom: 6px; }
        .stat-value { font-size: 1.5em; font-weight: bold; color: #0d47a1; }
    </style>
</head>
<body>
    <div class="navbar">
        <span class="brand"><span class="icon">👤</span> بروفايل المستخدم</span>
        <div class="menu">
            <button title="رجوع" onclick="window.history.back()"><span>🔙</span></button>
            <button title="الرئيسية" onclick="window.location.href='/apps-dashboard/'"><span>🏠</span></button>
            <button title="خروج" onclick="logout()" style="background:#b71c1c;"><span>🔓</span></button>
        </div>
    </div>
    <div class="profile-container" id="profile-container">
        <div id="profile-content">جاري التحميل...</div>
    </div>
    <script>
    function logout() {
        localStorage.removeItem('dashboard_logged_in');
        localStorage.removeItem('dashboard_email');
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/static/login.html';
    }
    async function fetchUserPreferences(token) {
        try {
            const res = await fetch(`/api/user/preferences/`, {
                headers: { 'Authorization': 'Bearer ' + token }
            });
            if (!res.ok) throw new Error('فشل في جلب التفضيلات');
            const prefs = await res.json();
            console.log('prefs:', prefs); // طباعة الجيسون القادم من السيرفر
            let brands = (prefs.favorite_brands && prefs.favorite_brands.length) ? prefs.favorite_brands.map(b => `<span style="background:#e3f7e3;color:#1976d2;padding:2px 10px;border-radius:8px;margin-left:6px;">${b.name}</span>`).join('') : '<span style="color:#888">لا يوجد</span>';
            let products = (prefs.favorite_products && prefs.favorite_products.length)
                ? prefs.favorite_products.slice(0, 10).map(p => `
                    <div class="stat-card" style="margin-bottom:10px;display:inline-block;min-width:120px;">
                        <a href="/products/${p.id}/" style="color:#0d47a1;text-decoration:none;">
                            <div style="font-weight:bold;font-size:1.1em;">${p.name}</div>
                        </a>
                    </div>
                `).join('')
                : '<span style="color:#888">لا يوجد</span>';
            let html = `
                <div class="section-title">تفضيلات المستخدم</div>
                <div class="profile-details-row"><b>النطاق السعري المفضل:</b> ${prefs.min_price || '-'} - ${prefs.max_price || '-'}</div>
                <div class="profile-details-row"><b>البراندات المفضلة:</b> ${brands}</div>
                <div class="profile-details-row"><b>المنتجات المفضلة:</b><br>${products}</div>
            `;
            document.getElementById('preferences-section').innerHTML = html;
        } catch (e) {
            document.getElementById('preferences-section').innerHTML = '<div class="section-title">تفضيلات المستخدم</div><div style="color:#b71c1c">فشل في جلب التفضيلات.</div>';
        }
    }

    async function fetchUserProfile() {
        const params = new URLSearchParams(window.location.search);
        const userId = params.get('id');
        if (!userId) {
            document.getElementById('profile-content').textContent = 'لم يتم تحديد معرف المستخدم.';
            return;
        }
        const token = localStorage.getItem('access_token');
        try {
            const res = await fetch(`/api/auth/users/${userId}/`, {
                headers: { 'Authorization': 'Bearer ' + token }
            });
            if (!res.ok) throw new Error('فشل في جلب بيانات المستخدم');
            const user = await res.json();
            document.getElementById('profile-content').innerHTML = `
                <div class="profile-header">
                    <img class="avatar" src="${user.avatar || '/static/user.png'}" onerror="this.onerror=null;this.src='/static/user.png';">
                    <div class="profile-info">
                        <h2>${user.username || ''}</h2>
                        <span class="role${user.is_admin ? ' admin' : ''}">${user.is_admin ? 'أدمن' : (user.is_owner ? 'مالك متجر' : 'مستخدم')}</span>
                        <div class="profile-details-row"><b>البريد:</b> ${user.email || ''}</div>
                        <div class="profile-details-row"><b>رقم الجوال:</b> ${user.phone || '-'}</div>
                        <div class="profile-details-row"><b>تاريخ التسجيل:</b> ${user.date_joined ? user.date_joined.split('T')[0] : '-'}</div>
                    </div>
                </div>
                <div id="preferences-section"></div>
            `;
            fetchUserPreferences(token);
        } catch (e) {
            document.getElementById('profile-content').textContent = 'فشل في جلب بيانات المستخدم.';
        }
    }
    fetchUserProfile();
    </script>
</body>
</html>
