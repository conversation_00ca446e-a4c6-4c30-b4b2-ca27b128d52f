<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - لوحة تحكم التطبيقات</title>
    <meta name="description" content="إدارة المستخدمين: تحكم كامل في إضافة وتعديل وحذف المستخدمين وصلاحياتهم من لوحة التحكم.">
    <link rel="stylesheet" href="/static/dashboard_style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        .icon-btn { background: none; border: none; font-size: 1.2em; cursor: pointer; margin: 0 2px; }
        .icon-btn.edit { color: #1976d2; }
        .icon-btn.delete { color: #b71c1c; }
        .icon-btn.edit:hover { color: #0d47a1; }
        .icon-btn.delete:hover { color: #d32f2f; }
        .form-popup { display: none; position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; background: rgba(30,60,120,0.13); z-index: 1001; align-items: center; justify-content: center; }
        .form-popup.active { display: flex; }
        .form-box { background: #fff; border-radius: 12px; box-shadow: 0 4px 24px rgba(30,60,120,0.13); padding: 32px 24px; min-width: 320px; }
        .form-box label { display: block; margin-bottom: 6px; color: #263238; }
        .form-box input, .form-box select { width: 100%; margin-bottom: 14px; padding: 8px; border-radius: 6px; border: 1px solid #bdbdbd; font-size: 1em; background: #f7fafd; }
        .form-box .form-actions { display: flex; justify-content: flex-end; gap: 10px; }
        .form-box button { background: #1976d2; color: #fff; border: none; border-radius: 6px; padding: 8px 20px; font-size: 1em; cursor: pointer; }
        .form-box button.cancel { background: #bdbdbd; color: #263238; }
        .form-box button:hover:not(.cancel) { background: #0d47a1; }
        .error { color: #b71c1c; text-align: center; margin-bottom: 12px; }
        .success { color: #388e3c; text-align: center; margin-bottom: 12px; }
        .crud-table {
            width: 100%;
            border-collapse: collapse;
            background: #fff;
            border-radius: 14px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(30,60,120,0.07);
            font-size: 1.08em;
        }
        .crud-table thead {
            background: linear-gradient(90deg, #1976d2 0%, #0d47a1 100%);
            color: #fff;
        }
        .crud-table th, .crud-table td {
            padding: 16px 10px;
            text-align: center;
        }
        .crud-table th {
            font-size: 1.1em;
            letter-spacing: 1px;
            border-bottom: 2px solid #1976d2;
        }
        .crud-table tbody tr {
            transition: background 0.18s;
        }
        .crud-table tbody tr:nth-child(even) {
            background: #f4f7fb;
        }
        .crud-table tbody tr:nth-child(odd) {
            background: #fafdff;
        }
        .crud-table tbody tr:hover {
            background: #e3e7ee;
            box-shadow: 0 2px 8px #1976d22a;
        }
        .crud-table td {
            border-bottom: 1px solid #e3e7ee;
        }
        .navbar {
            background: linear-gradient(90deg, #1976d2 0%, #0d47a1 100%);
            color: #fff;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 68px;
            box-shadow: 0 4px 18px rgba(30,60,120,0.13);
            border-radius: 0 0 18px 18px;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            z-index: 100;
        }
        body { padding-top: 80px !important; }
        .navbar .brand {
            font-size: 1.6em;
            font-weight: bold;
            padding: 0 40px;
            display: flex;
            align-items: center;
            gap: 12px;
            letter-spacing: 1px;
        }
        .navbar .brand .icon {
            font-size: 2em;
            margin-left: 8px;
            filter: drop-shadow(0 2px 4px #0d47a1aa);
        }
        .navbar .menu {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 0 32px;
        }
        .navbar .menu button {
            background: rgba(255,255,255,0.13);
            color: #fff;
            border: none;
            border-radius: 50%;
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            cursor: pointer;
            transition: background 0.2s, transform 0.2s;
            box-shadow: 0 2px 8px rgba(30,60,120,0.10);
        }
        .navbar .menu button:hover {
            background: #fff;
            color: #1976d2;
            transform: translateY(-2px) scale(1.08);
        }
        @media (max-width: 700px) {
            .crud-table th, .crud-table td { padding: 10px 4px; font-size: 0.98em; }
            .navbar .brand { font-size: 1.1em; padding: 0 10px; }
            .navbar .menu { padding: 0 8px; }
        }
    </style>
</head>
<body>
    <div class="navbar">
        <span class="brand"><span class="icon">👤</span> إدارة المستخدمين</span>
        <div class="menu">
            <button title="الرئيسية" onclick="window.location.href='/apps-dashboard/'"><span>🏠</span></button>
            <button title="خروج" onclick="logout()" style="background:#b71c1c;"><span>🔓</span></button>
        </div>
    </div>
    <div class="dashboard">
        <h1><span style="font-size:1.2em;vertical-align:middle;">👤</span> إدارة المستخدمين <span style="font-size:0.7em;color:#1976d2;"></span></h1>
        <div class="actions">
            <button class="add-btn" onclick="openForm()"><span style="font-size:1.1em;">➕</span> إضافة مستخدم جديد</button>
        </div>
        <div class="actions">
            <button onclick="filterUsers('all')"><span style="font-size:1.1em;">👥</span> الكل</button>
            <button onclick="filterUsers('admin')"><span style="font-size:1.1em;">🛡️</span> المدراء فقط</button>
            <button onclick="filterUsers('owner')"><span style="font-size:1.1em;">🏪</span> المالكون فقط</button>
            <button onclick="filterUsers('customer')"><span style="font-size:1.1em;">🛒</span> العملاء فقط</button>
        </div>
        <div style="width:100%;overflow-x:auto;">
            <table id="usersTable" class="crud-table" style="width:100%;min-width:700px;">
                <thead>
                    <tr><th>👤</th><th>📧</th><th>🛡️</th><th>⚙️</th></tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
    </div>
    <div class="form-popup" id="formPopup">
        <form class="form-box" onsubmit="return saveUser(event)">
            <div id="formError" class="error"></div>
            <label>الاسم</label>
            <input type="text" id="formName" required>
            <label>البريد الإلكتروني</label>
            <input type="email" id="formEmail" required>
            <label>الدور</label>
            <select id="formRole">
                <option value="admin">مدير</option>
                <option value="owner">مالك</option>
                <option value="customer">عميل</option>
            </select>
            <label>كلمة المرور</label>
            <input type="password" id="formPassword" required>
            <div class="form-actions">
                <button type="button" class="cancel" onclick="closeForm()">إلغاء</button>
                <button type="submit">حفظ</button>
            </div>
        </form>
    </div>
    <script>
        let users = [];
        let editIndex = null;
        let filterRole = 'all';
        async function fetchUsers() {
            const token = localStorage.getItem('access_token');
            const res = await fetch('/api/auth/users/', { headers: { 'Authorization': 'Bearer ' + token } });
            if (res.ok) {
                users = await res.json();
                renderUsers();
            } else {
                users = [];
                renderUsers();
            }
        }
        function renderUsers() {
            const tbody = document.querySelector('#usersTable tbody');
            tbody.innerHTML = '';
            let filtered = users;
            if (filterRole !== 'all') {
                filtered = users.filter(u => (u.role || u.user_type) === filterRole);
            }
            filtered.forEach((u, i) => {
                tbody.innerHTML += `<tr style="cursor:pointer;" onclick="window.location.href='/static/user_profile.html?id=${u.id}'">
                    <td>👤 ${u.username}</td>
                    <td>📧 ${u.email}</td>
                    <td>🛡️ ${u.role_display || u.role || u.user_type || ''}</td>
                    <td>
                        <button class="icon-btn delete" title="حذف" onclick="event.stopPropagation();deleteUser(${i});return false;">🗑️</button>
                        <button class="icon-btn" title="تفعيل/تعطيل" onclick="event.stopPropagation();toggleActiveUser(${i});return false;">${u.is_active ? '🚫' : '✅'}</button>
                    </td>
                </tr>`;
            });
            if(filtered.length === 0) tbody.innerHTML = '<tr><td colspan="4">لا يوجد مستخدمون</td></tr>';
        }
        function filterUsers(role) {
            filterRole = role;
            renderUsers();
        }
        function openForm(editI) {
            document.getElementById('formPopup').classList.add('active');
            document.getElementById('formError').textContent = '';
            if(editI !== undefined) {
                editIndex = editI;
                const u = users[editI];
                document.getElementById('formName').value = u.username;
                document.getElementById('formEmail').value = u.email;
                document.getElementById('formRole').value = u.user_type;
                document.getElementById('formPassword').value = '';
            } else {
                editIndex = null;
                document.getElementById('formName').value = '';
                document.getElementById('formEmail').value = '';
                document.getElementById('formRole').value = 'customer';
                document.getElementById('formPassword').value = '';
            }
        }
        function closeForm() {
            document.getElementById('formPopup').classList.remove('active');
        }
        async function saveUser(e) {
            e.preventDefault();
            const username = document.getElementById('formName').value.trim();
            const email = document.getElementById('formEmail').value.trim();
            const user_type = document.getElementById('formRole').value;
            const password = document.getElementById('formPassword').value;
            if(!username || !email || !password) {
                document.getElementById('formError').textContent = 'جميع الحقول مطلوبة';
                return false;
            }
            const token = localStorage.getItem('access_token');
            let res;
            if(editIndex !== null) {
                // تحديث مستخدم (تحتاج API خاص للتعديل)
                res = await fetch(`/api/auth/users/${users[editIndex].id}/`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer ' + token },
                    body: JSON.stringify({ username, email, user_type, password })
                });
            } else {
                // إضافة مستخدم جديد
                res = await fetch('/api/auth/register/', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer ' + token },
                    body: JSON.stringify({ username, email, user_type, password, password2: password })
                });
            }
            if(res.ok) {
                closeForm();
                fetchUsers();
            } else {
                const data = await res.json();
                document.getElementById('formError').textContent = data.error || 'حدث خطأ أثناء الحفظ';
            }
            return false;
        }
        function editUser(i) { openForm(i); }
        async function deleteUser(i) {
            if(confirm('هل أنت متأكد من حذف المستخدم؟')) {
                const token = localStorage.getItem('access_token');
                const res = await fetch(`/api/auth/users/${users[i].id}/`, {
                    method: 'DELETE',
                    headers: { 'Authorization': 'Bearer ' + token }
                });
                if(res.ok) fetchUsers();
            }
        }
        async function toggleActiveUser(i) {
            const token = localStorage.getItem('access_token');
            const user = users[i];
            const res = await fetch(`/api/auth/users/${user.id}/`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer ' + token },
                body: JSON.stringify({ is_active: !user.is_active })
            });
            if(res.ok) fetchUsers();
        }
        fetchUsers();
    </script>
</body>
</html>
