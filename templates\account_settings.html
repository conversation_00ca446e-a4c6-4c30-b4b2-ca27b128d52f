{% extends 'base.html' %}

{% block title %}Account Settings - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="Manage your Best in Click account settings, security, privacy, and preferences.">
<style>
  .settings-header {
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
    color: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
  }
  .settings-nav {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 1rem;
    margin-bottom: 2rem;
    position: sticky;
    top: 100px;
  }
  .settings-nav .nav-link {
    color: #495057;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    margin-bottom: 0.25rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
  }
  .settings-nav .nav-link i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
  }
  .settings-nav .nav-link:hover,
  .settings-nav .nav-link.active {
    background: #6f42c1;
    color: white;
  }
  .settings-section {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-bottom: 2rem;
    display: none;
  }
  .settings-section.active {
    display: block;
  }
  .section-title {
    color: #6f42c1;
    font-weight: bold;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
    display: flex;
    align-items: center;
  }
  .section-title i {
    margin-right: 0.75rem;
  }
  .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
  }
  .setting-item:hover {
    border-color: #6f42c1;
    background: #f8f9fa;
  }
  .setting-info h6 {
    margin-bottom: 0.25rem;
    font-weight: 600;
  }
  .setting-info small {
    color: #6c757d;
  }
  .danger-zone {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 1rem;
    padding: 2rem;
    margin-top: 2rem;
  }
  .danger-zone h5 {
    color: #e53e3e;
    margin-bottom: 1rem;
  }
  .two-factor-setup {
    background: #e3f0ff;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  .security-status {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
  }
  .security-badge {
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: bold;
    margin-right: 1rem;
  }
  .security-badge.strong {
    background: #d4edda;
    color: #155724;
  }
  .security-badge.medium {
    background: #fff3cd;
    color: #856404;
  }
  .security-badge.weak {
    background: #f8d7da;
    color: #721c24;
  }
  .activity-log {
    max-height: 400px;
    overflow-y: auto;
  }
  .activity-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #f8f9fa;
  }
  .activity-item:last-child {
    border-bottom: none;
  }
  .activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    margin-right: 1rem;
    flex-shrink: 0;
  }
  .activity-icon.login { background: #28a745; }
  .activity-icon.logout { background: #dc3545; }
  .activity-icon.settings { background: #17a2b8; }
  .activity-icon.password { background: #ffc107; color: #000; }
  .export-data {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  .data-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
  }
  .connected-account {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
  }
  .connected-account.connected {
    border-color: #28a745;
    background: #f8fff9;
  }
  .account-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
  }
  .account-icon.google { background: #db4437; }
  .account-icon.facebook { background: #4267B2; }
  .account-icon.apple { background: #000; }
  .account-icon.twitter { background: #1da1f2; }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
  <!-- Settings Header -->
  <div class="settings-header">
    <div class="row align-items-center">
      <div class="col-md-8">
        <h1 class="fw-bold mb-2">
          <i class="fas fa-cog me-3"></i>Account Settings
        </h1>
        <p class="mb-0">Manage your account security, privacy, and preferences</p>
      </div>
      <div class="col-md-4 text-end">
        <a href="/profile/" class="btn btn-light">
          <i class="fas fa-user me-2"></i>View Profile
        </a>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Settings Navigation -->
    <div class="col-lg-3">
      <div class="settings-nav">
        <nav class="nav flex-column">
          <a class="nav-link active" href="#" onclick="showSection('general')">
            <i class="fas fa-user-cog"></i>General
          </a>
          <a class="nav-link" href="#" onclick="showSection('security')">
            <i class="fas fa-shield-alt"></i>Security
          </a>
          <a class="nav-link" href="#" onclick="showSection('privacy')">
            <i class="fas fa-eye-slash"></i>Privacy
          </a>
          <a class="nav-link" href="#" onclick="showSection('notifications')">
            <i class="fas fa-bell"></i>Notifications
          </a>
          <a class="nav-link" href="#" onclick="showSection('connected')">
            <i class="fas fa-link"></i>Connected Accounts
          </a>
          <a class="nav-link" href="#" onclick="showSection('data')">
            <i class="fas fa-download"></i>Data & Export
          </a>
          <a class="nav-link" href="#" onclick="showSection('billing')">
            <i class="fas fa-credit-card"></i>Billing
          </a>
        </nav>
      </div>
    </div>

    <!-- Settings Content -->
    <div class="col-lg-9">
      <!-- General Settings -->
      <div class="settings-section active" id="general">
        <h3 class="section-title">
          <i class="fas fa-user-cog"></i>General Settings
        </h3>
        
        <div class="setting-item">
          <div class="setting-info">
            <h6>Language</h6>
            <small>Choose your preferred language for the interface</small>
          </div>
          <select class="form-select" style="width: auto;">
            <option value="en">English</option>
            <option value="ar">العربية</option>
            <option value="es">Español</option>
            <option value="fr">Français</option>
          </select>
        </div>
        
        <div class="setting-item">
          <div class="setting-info">
            <h6>Time Zone</h6>
            <small>Set your local time zone for accurate timestamps</small>
          </div>
          <select class="form-select" style="width: auto;">
            <option value="UTC">UTC</option>
            <option value="America/New_York">Eastern Time</option>
            <option value="America/Los_Angeles">Pacific Time</option>
            <option value="Europe/London">London</option>
            <option value="Asia/Dubai">Dubai</option>
          </select>
        </div>
        
        <div class="setting-item">
          <div class="setting-info">
            <h6>Currency</h6>
            <small>Default currency for prices and transactions</small>
          </div>
          <select class="form-select" style="width: auto;">
            <option value="USD">USD ($)</option>
            <option value="EUR">EUR (€)</option>
            <option value="GBP">GBP (£)</option>
            <option value="AED">AED (د.إ)</option>
          </select>
        </div>
        
        <div class="setting-item">
          <div class="setting-info">
            <h6>Theme</h6>
            <small>Choose between light and dark mode</small>
          </div>
          <select class="form-select" style="width: auto;">
            <option value="light">Light</option>
            <option value="dark">Dark</option>
            <option value="auto">Auto (System)</option>
          </select>
        </div>
        
        <div class="text-end">
          <button class="btn btn-primary" onclick="saveGeneralSettings()">
            <i class="fas fa-save me-2"></i>Save Changes
          </button>
        </div>
      </div>

      <!-- Security Settings -->
      <div class="settings-section" id="security">
        <h3 class="section-title">
          <i class="fas fa-shield-alt"></i>Security Settings
        </h3>
        
        <!-- Security Status -->
        <div class="security-status">
          <span class="security-badge strong">
            <i class="fas fa-shield-check me-1"></i>Strong Security
          </span>
          <span>Your account is well protected</span>
        </div>
        
        <div class="setting-item">
          <div class="setting-info">
            <h6>Password</h6>
            <small>Last changed 3 months ago</small>
          </div>
          <button class="btn btn-outline-primary" onclick="changePassword()">
            Change Password
          </button>
        </div>
        
        <div class="setting-item">
          <div class="setting-info">
            <h6>Two-Factor Authentication</h6>
            <small>Add an extra layer of security to your account</small>
          </div>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="twoFactorAuth">
            <label class="form-check-label" for="twoFactorAuth">Enable 2FA</label>
          </div>
        </div>
        
        <div class="setting-item">
          <div class="setting-info">
            <h6>Login Notifications</h6>
            <small>Get notified when someone logs into your account</small>
          </div>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="loginNotifications" checked>
            <label class="form-check-label" for="loginNotifications">Enable</label>
          </div>
        </div>
        
        <div class="setting-item">
          <div class="setting-info">
            <h6>Active Sessions</h6>
            <small>Manage devices that are currently logged in</small>
          </div>
          <button class="btn btn-outline-danger" onclick="viewActiveSessions()">
            View Sessions
          </button>
        </div>
        
        <!-- Recent Activity -->
        <h5 class="mt-4 mb-3">Recent Security Activity</h5>
        <div class="activity-log">
          <div class="activity-item">
            <div class="activity-icon login">
              <i class="fas fa-sign-in-alt"></i>
            </div>
            <div>
              <h6 class="mb-1">Successful login</h6>
              <small class="text-muted">Chrome on Windows • 2 hours ago</small>
            </div>
          </div>
          
          <div class="activity-item">
            <div class="activity-icon settings">
              <i class="fas fa-cog"></i>
            </div>
            <div>
              <h6 class="mb-1">Settings updated</h6>
              <small class="text-muted">Notification preferences changed • 1 day ago</small>
            </div>
          </div>
          
          <div class="activity-item">
            <div class="activity-icon password">
              <i class="fas fa-key"></i>
            </div>
            <div>
              <h6 class="mb-1">Password changed</h6>
              <small class="text-muted">Password updated successfully • 3 months ago</small>
            </div>
          </div>
        </div>
      </div>

      <!-- Privacy Settings -->
      <div class="settings-section" id="privacy">
        <h3 class="section-title">
          <i class="fas fa-eye-slash"></i>Privacy Settings
        </h3>
        
        <div class="setting-item">
          <div class="setting-info">
            <h6>Profile Visibility</h6>
            <small>Control who can see your profile information</small>
          </div>
          <select class="form-select" style="width: auto;">
            <option value="public">Public</option>
            <option value="friends">Friends Only</option>
            <option value="private">Private</option>
          </select>
        </div>
        
        <div class="setting-item">
          <div class="setting-info">
            <h6>Show Online Status</h6>
            <small>Let others see when you're online</small>
          </div>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="showOnlineStatus" checked>
          </div>
        </div>
        
        <div class="setting-item">
          <div class="setting-info">
            <h6>Activity Tracking</h6>
            <small>Allow us to track your activity for better recommendations</small>
          </div>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="activityTracking" checked>
          </div>
        </div>
        
        <div class="setting-item">
          <div class="setting-info">
            <h6>Data Analytics</h6>
            <small>Help improve our service by sharing anonymous usage data</small>
          </div>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="dataAnalytics">
          </div>
        </div>
        
        <div class="setting-item">
          <div class="setting-info">
            <h6>Marketing Communications</h6>
            <small>Receive promotional emails and offers</small>
          </div>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="marketingComms" checked>
          </div>
        </div>
      </div>

      <!-- Notification Settings -->
      <div class="settings-section" id="notifications">
        <h3 class="section-title">
          <i class="fas fa-bell"></i>Notification Settings
        </h3>
        
        <div class="row">
          <div class="col-md-6">
            <h5 class="mb-3">Email Notifications</h5>
            
            <div class="setting-item">
              <div class="setting-info">
                <h6>Price Alerts</h6>
                <small>When prices drop on wishlist items</small>
              </div>
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="emailPriceAlerts" checked>
              </div>
            </div>
            
            <div class="setting-item">
              <div class="setting-info">
                <h6>Order Updates</h6>
                <small>Order confirmations and shipping updates</small>
              </div>
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="emailOrders" checked>
              </div>
            </div>
            
            <div class="setting-item">
              <div class="setting-info">
                <h6>Weekly Newsletter</h6>
                <small>Weekly roundup of deals and recommendations</small>
              </div>
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="emailNewsletter" checked>
              </div>
            </div>
          </div>
          
          <div class="col-md-6">
            <h5 class="mb-3">Push Notifications</h5>
            
            <div class="setting-item">
              <div class="setting-info">
                <h6>Flash Sales</h6>
                <small>Instant notifications for limited-time deals</small>
              </div>
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="pushFlashSales">
              </div>
            </div>
            
            <div class="setting-item">
              <div class="setting-info">
                <h6>Wishlist Updates</h6>
                <small>When wishlist items go on sale</small>
              </div>
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="pushWishlist" checked>
              </div>
            </div>
            
            <div class="setting-item">
              <div class="setting-info">
                <h6>Review Reminders</h6>
                <small>Reminders to review purchased products</small>
              </div>
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="pushReviews" checked>
              </div>
            </div>
          </div>
        </div>
        
        <div class="text-end mt-3">
          <button class="btn btn-primary" onclick="saveNotificationSettings()">
            <i class="fas fa-save me-2"></i>Save Notification Settings
          </button>
        </div>
      </div>

      <!-- Connected Accounts -->
      <div class="settings-section" id="connected">
        <h3 class="section-title">
          <i class="fas fa-link"></i>Connected Accounts
        </h3>
        
        <div class="connected-account connected">
          <div class="account-icon google">
            <i class="fab fa-google"></i>
          </div>
          <div class="flex-grow-1">
            <h6 class="mb-1">Google</h6>
            <small class="text-muted">Connected • Use for quick login</small>
          </div>
          <button class="btn btn-outline-danger btn-sm" onclick="disconnectAccount('google')">
            Disconnect
          </button>
        </div>
        
        <div class="connected-account">
          <div class="account-icon facebook">
            <i class="fab fa-facebook-f"></i>
          </div>
          <div class="flex-grow-1">
            <h6 class="mb-1">Facebook</h6>
            <small class="text-muted">Not connected</small>
          </div>
          <button class="btn btn-primary btn-sm" onclick="connectAccount('facebook')">
            Connect
          </button>
        </div>
        
        <div class="connected-account">
          <div class="account-icon apple">
            <i class="fab fa-apple"></i>
          </div>
          <div class="flex-grow-1">
            <h6 class="mb-1">Apple</h6>
            <small class="text-muted">Not connected</small>
          </div>
          <button class="btn btn-primary btn-sm" onclick="connectAccount('apple')">
            Connect
          </button>
        </div>
        
        <div class="connected-account">
          <div class="account-icon twitter">
            <i class="fab fa-twitter"></i>
          </div>
          <div class="flex-grow-1">
            <h6 class="mb-1">Twitter</h6>
            <small class="text-muted">Not connected</small>
          </div>
          <button class="btn btn-primary btn-sm" onclick="connectAccount('twitter')">
            Connect
          </button>
        </div>
      </div>

      <!-- Data & Export -->
      <div class="settings-section" id="data">
        <h3 class="section-title">
          <i class="fas fa-download"></i>Data & Export
        </h3>
        
        <div class="export-data">
          <h5 class="mb-3">Export Your Data</h5>
          <p class="text-muted mb-4">Download a copy of your data including profile information, orders, reviews, and more.</p>
          
          <div class="data-item">
            <div>
              <h6 class="mb-1">Profile Data</h6>
              <small class="text-muted">Personal information, preferences, and settings</small>
            </div>
            <button class="btn btn-outline-primary btn-sm" onclick="exportData('profile')">
              <i class="fas fa-download me-1"></i>Export
            </button>
          </div>
          
          <div class="data-item">
            <div>
              <h6 class="mb-1">Order History</h6>
              <small class="text-muted">Complete history of your purchases and transactions</small>
            </div>
            <button class="btn btn-outline-primary btn-sm" onclick="exportData('orders')">
              <i class="fas fa-download me-1"></i>Export
            </button>
          </div>
          
          <div class="data-item">
            <div>
              <h6 class="mb-1">Reviews & Ratings</h6>
              <small class="text-muted">All reviews and ratings you've submitted</small>
            </div>
            <button class="btn btn-outline-primary btn-sm" onclick="exportData('reviews')">
              <i class="fas fa-download me-1"></i>Export
            </button>
          </div>
          
          <div class="text-center mt-4">
            <button class="btn btn-primary btn-lg" onclick="exportAllData()">
              <i class="fas fa-download me-2"></i>Export All Data
            </button>
          </div>
        </div>
      </div>

      <!-- Billing -->
      <div class="settings-section" id="billing">
        <h3 class="section-title">
          <i class="fas fa-credit-card"></i>Billing & Subscription
        </h3>
        
        <div class="alert alert-info">
          <i class="fas fa-info-circle me-2"></i>
          You're currently on the <strong>Free Plan</strong>. Upgrade to Premium for exclusive features and benefits.
        </div>
        
        <div class="row">
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h6 class="mb-0">Current Plan</h6>
              </div>
              <div class="card-body">
                <h4 class="text-primary">Free Plan</h4>
                <ul class="list-unstyled">
                  <li><i class="fas fa-check text-success me-2"></i>Basic product search</li>
                  <li><i class="fas fa-check text-success me-2"></i>Price comparison</li>
                  <li><i class="fas fa-check text-success me-2"></i>Basic recommendations</li>
                  <li><i class="fas fa-times text-danger me-2"></i>Advanced AI features</li>
                  <li><i class="fas fa-times text-danger me-2"></i>Priority support</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div class="col-md-6">
            <div class="card border-primary">
              <div class="card-header bg-primary text-white">
                <h6 class="mb-0">Premium Plan</h6>
              </div>
              <div class="card-body">
                <h4 class="text-primary">$9.99/month</h4>
                <ul class="list-unstyled">
                  <li><i class="fas fa-check text-success me-2"></i>Everything in Free</li>
                  <li><i class="fas fa-check text-success me-2"></i>Advanced AI recommendations</li>
                  <li><i class="fas fa-check text-success me-2"></i>Price alerts & notifications</li>
                  <li><i class="fas fa-check text-success me-2"></i>Priority customer support</li>
                  <li><i class="fas fa-check text-success me-2"></i>Exclusive deals access</li>
                </ul>
                <button class="btn btn-primary w-100" onclick="upgradeToPremium()">
                  Upgrade Now
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Danger Zone -->
      <div class="danger-zone">
        <h5>
          <i class="fas fa-exclamation-triangle me-2"></i>Danger Zone
        </h5>
        <p class="text-muted mb-3">These actions are permanent and cannot be undone.</p>
        
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h6 class="text-danger mb-1">Delete Account</h6>
            <small class="text-muted">Permanently delete your account and all associated data</small>
          </div>
          <button class="btn btn-outline-danger" onclick="deleteAccount()">
            Delete Account
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function showSection(sectionId) {
  // Hide all sections
  document.querySelectorAll('.settings-section').forEach(section => {
    section.classList.remove('active');
  });
  
  // Remove active class from all nav links
  document.querySelectorAll('.settings-nav .nav-link').forEach(link => {
    link.classList.remove('active');
  });
  
  // Show selected section
  document.getElementById(sectionId).classList.add('active');
  
  // Add active class to clicked nav link
  event.target.classList.add('active');
}

function saveGeneralSettings() {
  console.log('Saving general settings...');
  showToast('General settings saved successfully!', 'success');
}

function changePassword() {
  // This would open a password change modal or redirect to a password change page
  alert('Password change functionality would be implemented here');
}

function viewActiveSessions() {
  alert('Active sessions management would be implemented here');
}

function saveNotificationSettings() {
  console.log('Saving notification settings...');
  showToast('Notification settings saved successfully!', 'success');
}

function connectAccount(provider) {
  console.log('Connecting to', provider);
  showToast(`Connecting to ${provider}...`, 'info');
}

function disconnectAccount(provider) {
  if (confirm(`Are you sure you want to disconnect your ${provider} account?`)) {
    console.log('Disconnecting from', provider);
    showToast(`${provider} account disconnected`, 'success');
  }
}

function exportData(type) {
  console.log('Exporting', type, 'data');
  showToast(`Preparing ${type} data export...`, 'info');
}

function exportAllData() {
  console.log('Exporting all data');
  showToast('Preparing complete data export...', 'info');
}

function upgradeToPremium() {
  alert('Premium upgrade functionality would be implemented here');
}

function deleteAccount() {
  const confirmation = prompt('Type "DELETE" to confirm account deletion:');
  if (confirmation === 'DELETE') {
    alert('Account deletion functionality would be implemented here');
  } else if (confirmation !== null) {
    showToast('Account deletion cancelled - incorrect confirmation', 'warning');
  }
}

function showToast(message, type) {
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} position-fixed`;
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
  toast.innerHTML = `
    ${message}
    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
  `;
  document.body.appendChild(toast);
  
  setTimeout(() => {
    if (toast.parentElement) {
      toast.remove();
    }
  }, 3000);
}

// Auto-save settings on change
document.querySelectorAll('input, select').forEach(input => {
  input.addEventListener('change', function() {
    // Auto-save logic would go here
    console.log('Auto-saving setting:', this.id || this.name);
  });
});
</script>
{% endblock %}
