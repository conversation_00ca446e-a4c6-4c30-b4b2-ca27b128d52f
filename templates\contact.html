{% extends 'base.html' %}
{% load static %}

{% block title %}Contact Us - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="Get in touch with <PERSON> in Click. Contact our support team, visit our offices, or connect with us on social media.">
<meta property="og:title" content="Contact Best in Click - We're Here to Help">
<meta property="og:description" content="Reach out to our team for support, partnerships, or any questions about our e-commerce platform.">
<style>
  .contact-hero {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 3rem;
  }
  .contact-card {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
  }
  .contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.15);
  }
  .contact-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #0d6efd, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
  }
  .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }
  .office-card {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    height: 100%;
  }
  .social-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: transform 0.3s ease;
    margin: 0 0.5rem;
  }
  .social-icon:hover {
    transform: scale(1.1);
    color: white;
  }
  .map-container {
    height: 300px;
    background: #e9ecef;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
  }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="contact-hero">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-8">
        <h1 class="display-4 fw-bold mb-4">Get in Touch</h1>
        <p class="lead mb-0">We'd love to hear from you. Send us a message and we'll respond as soon as possible.</p>
      </div>
      <div class="col-lg-4 text-center">
        <i class="fas fa-comments" style="font-size: 6rem; opacity: 0.3;"></i>
      </div>
    </div>
  </div>
</div>

<div class="container">
  <!-- Contact Methods -->
  <div class="row mb-5">
    <div class="col-lg-4 mb-4">
      <div class="contact-card">
        <div class="card-body text-center p-4">
          <div class="contact-icon">
            <i class="fas fa-envelope"></i>
          </div>
          <h5 class="card-title">Email Us</h5>
          <p class="card-text text-muted">Get in touch via email for detailed inquiries</p>
          <a href="mailto:<EMAIL>" class="btn btn-primary"><EMAIL></a>
        </div>
      </div>
    </div>
    
    <div class="col-lg-4 mb-4">
      <div class="contact-card">
        <div class="card-body text-center p-4">
          <div class="contact-icon">
            <i class="fas fa-phone"></i>
          </div>
          <h5 class="card-title">Call Us</h5>
          <p class="card-text text-muted">Speak directly with our support team</p>
          <a href="tel:******-BEST-CLICK" class="btn btn-primary">+1 (800) BEST-CLICK</a>
        </div>
      </div>
    </div>
    
    <div class="col-lg-4 mb-4">
      <div class="contact-card">
        <div class="card-body text-center p-4">
          <div class="contact-icon">
            <i class="fas fa-comments"></i>
          </div>
          <h5 class="card-title">Live Chat</h5>
          <p class="card-text text-muted">Chat with us in real-time for instant help</p>
          <button class="btn btn-primary" onclick="openLiveChat()">Start Chat</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Contact Form and Office Info -->
  <div class="row">
    <div class="col-lg-8 mb-5">
      <div class="card border-0 shadow-sm">
        <div class="card-body p-4">
          <h3 class="fw-bold text-primary mb-4">Send us a Message</h3>
          
          {% if messages %}
            {% for message in messages %}
              <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
              </div>
            {% endfor %}
          {% endif %}
          
          <form method="post" id="contactForm">
            {% csrf_token %}
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="first_name" class="form-label">First Name *</label>
                <input type="text" class="form-control" id="first_name" name="first_name" required>
              </div>
              <div class="col-md-6 mb-3">
                <label for="last_name" class="form-label">Last Name *</label>
                <input type="text" class="form-control" id="last_name" name="last_name" required>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="email" class="form-label">Email Address *</label>
                <input type="email" class="form-control" id="email" name="email" required>
              </div>
              <div class="col-md-6 mb-3">
                <label for="phone" class="form-label">Phone Number</label>
                <input type="tel" class="form-control" id="phone" name="phone">
              </div>
            </div>
            
            <div class="mb-3">
              <label for="subject" class="form-label">Subject *</label>
              <select class="form-select" id="subject" name="subject" required>
                <option value="">Choose a subject...</option>
                <option value="general">General Inquiry</option>
                <option value="support">Technical Support</option>
                <option value="partnership">Partnership</option>
                <option value="feedback">Feedback</option>
                <option value="other">Other</option>
              </select>
            </div>
            
            <div class="mb-3">
              <label for="message" class="form-label">Message *</label>
              <textarea class="form-control" id="message" name="message" rows="5" required placeholder="Tell us how we can help you..."></textarea>
            </div>
            
            <div class="mb-3 form-check">
              <input type="checkbox" class="form-check-input" id="newsletter" name="newsletter">
              <label class="form-check-label" for="newsletter">
                Subscribe to our newsletter for updates and special offers
              </label>
            </div>
            
            <button type="submit" class="btn btn-primary btn-lg">
              <i class="fas fa-paper-plane me-2"></i>Send Message
            </button>
          </form>
        </div>
      </div>
    </div>
    
    <div class="col-lg-4">
      <!-- Office Locations -->
      <div class="mb-4">
        <h4 class="fw-bold text-primary mb-3">Our Offices</h4>
        
        <div class="office-card mb-3">
          <i class="fas fa-building text-primary fa-2x mb-2"></i>
          <h6 class="fw-bold">Headquarters</h6>
          <p class="mb-1">123 Innovation Drive</p>
          <p class="mb-1">San Francisco, CA 94105</p>
          <p class="text-muted">United States</p>
        </div>
        
        <div class="office-card mb-3">
          <i class="fas fa-building text-success fa-2x mb-2"></i>
          <h6 class="fw-bold">European Office</h6>
          <p class="mb-1">456 Tech Street</p>
          <p class="mb-1">London, EC2A 4DP</p>
          <p class="text-muted">United Kingdom</p>
        </div>
        
        <div class="office-card">
          <i class="fas fa-building text-warning fa-2x mb-2"></i>
          <h6 class="fw-bold">Asia Pacific</h6>
          <p class="mb-1">789 Digital Avenue</p>
          <p class="mb-1">Singapore 018956</p>
          <p class="text-muted">Singapore</p>
        </div>
      </div>
      
      <!-- Business Hours -->
      <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
          <h5 class="fw-bold text-primary mb-3">Business Hours</h5>
          <div class="d-flex justify-content-between mb-2">
            <span>Monday - Friday</span>
            <span class="fw-semibold">9:00 AM - 6:00 PM</span>
          </div>
          <div class="d-flex justify-content-between mb-2">
            <span>Saturday</span>
            <span class="fw-semibold">10:00 AM - 4:00 PM</span>
          </div>
          <div class="d-flex justify-content-between">
            <span>Sunday</span>
            <span class="text-muted">Closed</span>
          </div>
          <hr>
          <p class="small text-muted mb-0">
            <i class="fas fa-clock me-1"></i>
            All times are in Pacific Standard Time (PST)
          </p>
        </div>
      </div>
      
      <!-- Social Media -->
      <div class="text-center">
        <h5 class="fw-bold text-primary mb-3">Follow Us</h5>
        <div class="mb-3">
          <a href="#" class="social-icon" style="background: #1877f2;">
            <i class="fab fa-facebook-f"></i>
          </a>
          <a href="#" class="social-icon" style="background: #1da1f2;">
            <i class="fab fa-twitter"></i>
          </a>
          <a href="#" class="social-icon" style="background: #0077b5;">
            <i class="fab fa-linkedin-in"></i>
          </a>
          <a href="#" class="social-icon" style="background: #e4405f;">
            <i class="fab fa-instagram"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Map Section -->
  <div class="row mt-5">
    <div class="col-12">
      <h3 class="fw-bold text-primary text-center mb-4">Find Us</h3>
      <div class="map-container">
        <div class="text-center">
          <i class="fas fa-map-marker-alt fa-3x mb-3"></i>
          <h5>Interactive Map</h5>
          <p class="text-muted">Map integration would be implemented here</p>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function openLiveChat() {
  // Live chat integration would be implemented here
  alert('Live chat feature would be integrated here with services like Intercom, Zendesk, or custom solution');
}

// Form validation
document.getElementById('contactForm').addEventListener('submit', function(e) {
  const requiredFields = ['first_name', 'last_name', 'email', 'subject', 'message'];
  let isValid = true;
  
  requiredFields.forEach(field => {
    const input = document.getElementById(field);
    if (!input.value.trim()) {
      input.classList.add('is-invalid');
      isValid = false;
    } else {
      input.classList.remove('is-invalid');
    }
  });
  
  if (!isValid) {
    e.preventDefault();
    alert('Please fill in all required fields.');
  }
});
</script>
{% endblock %}
