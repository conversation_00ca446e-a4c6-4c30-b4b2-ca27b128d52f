{% extends 'base.html' %}
{% load static %}

{% block title %}Edit Profile - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="Edit your Best in Click profile information, preferences, and settings.">
<style>
  .edit-profile-header {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    color: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
  }
  .form-section {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-bottom: 2rem;
  }
  .section-title {
    color: #0d6efd;
    font-weight: bold;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
  }
  .avatar-upload {
    position: relative;
    display: inline-block;
  }
  .avatar-preview {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid #e9ecef;
    object-fit: cover;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  .avatar-preview:hover {
    border-color: #0d6efd;
    transform: scale(1.05);
  }
  .avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    cursor: pointer;
  }
  .avatar-upload:hover .avatar-overlay {
    opacity: 1;
  }
  .form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }
  .preference-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
  }
  .preference-item {
    border: 2px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  .preference-item:hover {
    border-color: #0d6efd;
    background: #e3f0ff;
  }
  .preference-item.selected {
    border-color: #0d6efd;
    background: #e3f0ff;
    color: #0d6efd;
  }
  .preference-item input[type="checkbox"] {
    display: none;
  }
  .preference-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
  }
  .save-actions {
    position: sticky;
    bottom: 20px;
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 -4px 15px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
  }
  .progress-indicator {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1rem;
    margin-bottom: 2rem;
  }
  .progress-step {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
  }
  .progress-step:last-child {
    margin-bottom: 0;
  }
  .step-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    font-size: 0.8rem;
  }
  .step-icon.completed {
    background: #28a745;
    color: white;
  }
  .step-icon.current {
    background: #0d6efd;
    color: white;
  }
  .step-icon.pending {
    background: #e9ecef;
    color: #6c757d;
  }
  .image-crop-modal .modal-dialog {
    max-width: 600px;
  }
  .crop-container {
    max-height: 400px;
    overflow: hidden;
  }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
  <!-- Header -->
  <div class="edit-profile-header">
    <div class="row align-items-center">
      <div class="col-md-8">
        <h1 class="fw-bold mb-2">Edit Profile</h1>
        <p class="mb-0">Update your personal information and preferences to get better recommendations</p>
      </div>
      <div class="col-md-4 text-end">
        <a href="/profile/" class="btn btn-light">
          <i class="fas fa-arrow-left me-2"></i>Back to Profile
        </a>
      </div>
    </div>
  </div>

  <!-- Progress Indicator -->
  <div class="progress-indicator">
    <h6 class="fw-bold mb-3">Profile Completion Progress</h6>
    <div class="row">
      <div class="col-md-4">
        <div class="progress-step">
          <div class="step-icon completed">
            <i class="fas fa-check"></i>
          </div>
          <span>Basic Information</span>
        </div>
      </div>
      <div class="col-md-4">
        <div class="progress-step">
          <div class="step-icon current">2</div>
          <span>Preferences</span>
        </div>
      </div>
      <div class="col-md-4">
        <div class="progress-step">
          <div class="step-icon pending">3</div>
          <span>Notifications</span>
        </div>
      </div>
    </div>
  </div>

  <form method="post" enctype="multipart/form-data" id="editProfileForm">
    {% csrf_token %}
    
    <!-- Error Messages -->
    {% if messages %}
      {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
          {{ message }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      {% endfor %}
    {% endif %}

    <div class="row">
      <!-- Left Column -->
      <div class="col-lg-8">
        <!-- Basic Information -->
        <div class="form-section">
          <h4 class="section-title">
            <i class="fas fa-user me-2"></i>Basic Information
          </h4>
          
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="firstName" class="form-label">First Name</label>
              <input type="text" class="form-control" id="firstName" name="first_name" 
                     value="{{ user.first_name }}" required>
            </div>
            
            <div class="col-md-6 mb-3">
              <label for="lastName" class="form-label">Last Name</label>
              <input type="text" class="form-control" id="lastName" name="last_name" 
                     value="{{ user.last_name }}" required>
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="email" class="form-label">Email Address</label>
              <input type="email" class="form-control" id="email" name="email" 
                     value="{{ user.email }}" required>
              <div class="form-text">We'll send important updates to this email</div>
            </div>
            
            <div class="col-md-6 mb-3">
              <label for="phone" class="form-label">Phone Number</label>
              <input type="tel" class="form-control" id="phone" name="phone" 
                     value="{{ user.profile.phone|default:'' }}" placeholder="+****************">
            </div>
          </div>
          
          <div class="mb-3">
            <label for="bio" class="form-label">Bio</label>
            <textarea class="form-control" id="bio" name="bio" rows="3" 
                      placeholder="Tell us a bit about yourself...">{{ user.profile.bio|default:'' }}</textarea>
            <div class="form-text">This will be visible on your public profile</div>
          </div>
          
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="location" class="form-label">Location</label>
              <input type="text" class="form-control" id="location" name="location" 
                     value="{{ user.profile.location|default:'' }}" placeholder="City, Country">
            </div>
            
            <div class="col-md-6 mb-3">
              <label for="website" class="form-label">Website</label>
              <input type="url" class="form-control" id="website" name="website" 
                     value="{{ user.profile.website|default:'' }}" placeholder="https://yourwebsite.com">
            </div>
          </div>
        </div>

        <!-- Shopping Preferences -->
        <div class="form-section">
          <h4 class="section-title">
            <i class="fas fa-shopping-cart me-2"></i>Shopping Preferences
          </h4>
          
          <div class="mb-4">
            <label class="form-label">Favorite Categories</label>
            <div class="preference-grid">
              <div class="preference-item" onclick="togglePreference('electronics')">
                <input type="checkbox" name="preferred_categories" value="electronics" id="electronics"
                       {% if 'electronics' in user_preferences %}checked{% endif %}>
                <i class="fas fa-laptop preference-icon"></i>
                <div>Electronics</div>
              </div>
              
              <div class="preference-item" onclick="togglePreference('fashion')">
                <input type="checkbox" name="preferred_categories" value="fashion" id="fashion"
                       {% if 'fashion' in user_preferences %}checked{% endif %}>
                <i class="fas fa-tshirt preference-icon"></i>
                <div>Fashion</div>
              </div>
              
              <div class="preference-item" onclick="togglePreference('home')">
                <input type="checkbox" name="preferred_categories" value="home" id="home"
                       {% if 'home' in user_preferences %}checked{% endif %}>
                <i class="fas fa-home preference-icon"></i>
                <div>Home & Garden</div>
              </div>
              
              <div class="preference-item" onclick="togglePreference('sports')">
                <input type="checkbox" name="preferred_categories" value="sports" id="sports"
                       {% if 'sports' in user_preferences %}checked{% endif %}>
                <i class="fas fa-dumbbell preference-icon"></i>
                <div>Sports</div>
              </div>
              
              <div class="preference-item" onclick="togglePreference('books')">
                <input type="checkbox" name="preferred_categories" value="books" id="books"
                       {% if 'books' in user_preferences %}checked{% endif %}>
                <i class="fas fa-book preference-icon"></i>
                <div>Books</div>
              </div>
              
              <div class="preference-item" onclick="togglePreference('gaming')">
                <input type="checkbox" name="preferred_categories" value="gaming" id="gaming"
                       {% if 'gaming' in user_preferences %}checked{% endif %}>
                <i class="fas fa-gamepad preference-icon"></i>
                <div>Gaming</div>
              </div>
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="budgetRange" class="form-label">Typical Budget Range</label>
              <select class="form-select" id="budgetRange" name="budget_range">
                <option value="">Select budget range</option>
                <option value="0-50" {% if user.profile.budget_range == '0-50' %}selected{% endif %}>$0 - $50</option>
                <option value="50-100" {% if user.profile.budget_range == '50-100' %}selected{% endif %}>$50 - $100</option>
                <option value="100-250" {% if user.profile.budget_range == '100-250' %}selected{% endif %}>$100 - $250</option>
                <option value="250-500" {% if user.profile.budget_range == '250-500' %}selected{% endif %}>$250 - $500</option>
                <option value="500+" {% if user.profile.budget_range == '500+' %}selected{% endif %}>$500+</option>
              </select>
            </div>
            
            <div class="col-md-6 mb-3">
              <label for="currency" class="form-label">Preferred Currency</label>
              <select class="form-select" id="currency" name="currency">
                <option value="USD" {% if user.profile.currency == 'USD' %}selected{% endif %}>USD ($)</option>
                <option value="EUR" {% if user.profile.currency == 'EUR' %}selected{% endif %}>EUR (€)</option>
                <option value="GBP" {% if user.profile.currency == 'GBP' %}selected{% endif %}>GBP (£)</option>
                <option value="CAD" {% if user.profile.currency == 'CAD' %}selected{% endif %}>CAD ($)</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Notification Preferences -->
        <div class="form-section">
          <h4 class="section-title">
            <i class="fas fa-bell me-2"></i>Notification Preferences
          </h4>
          
          <div class="row">
            <div class="col-md-6">
              <h6 class="fw-semibold mb-3">Email Notifications</h6>
              
              <div class="form-check mb-2">
                <input class="form-check-input" type="checkbox" id="emailDeals" name="email_deals"
                       {% if user.profile.email_deals %}checked{% endif %}>
                <label class="form-check-label" for="emailDeals">
                  Deals and Promotions
                </label>
              </div>
              
              <div class="form-check mb-2">
                <input class="form-check-input" type="checkbox" id="emailRecommendations" name="email_recommendations"
                       {% if user.profile.email_recommendations %}checked{% endif %}>
                <label class="form-check-label" for="emailRecommendations">
                  Product Recommendations
                </label>
              </div>
              
              <div class="form-check mb-2">
                <input class="form-check-input" type="checkbox" id="emailPriceAlerts" name="email_price_alerts"
                       {% if user.profile.email_price_alerts %}checked{% endif %}>
                <label class="form-check-label" for="emailPriceAlerts">
                  Price Drop Alerts
                </label>
              </div>
              
              <div class="form-check mb-2">
                <input class="form-check-input" type="checkbox" id="emailNewsletter" name="email_newsletter"
                       {% if user.profile.email_newsletter %}checked{% endif %}>
                <label class="form-check-label" for="emailNewsletter">
                  Weekly Newsletter
                </label>
              </div>
            </div>
            
            <div class="col-md-6">
              <h6 class="fw-semibold mb-3">Push Notifications</h6>
              
              <div class="form-check mb-2">
                <input class="form-check-input" type="checkbox" id="pushDeals" name="push_deals"
                       {% if user.profile.push_deals %}checked{% endif %}>
                <label class="form-check-label" for="pushDeals">
                  Flash Sales & Deals
                </label>
              </div>
              
              <div class="form-check mb-2">
                <input class="form-check-input" type="checkbox" id="pushWishlist" name="push_wishlist"
                       {% if user.profile.push_wishlist %}checked{% endif %}>
                <label class="form-check-label" for="pushWishlist">
                  Wishlist Price Changes
                </label>
              </div>
              
              <div class="form-check mb-2">
                <input class="form-check-input" type="checkbox" id="pushRestock" name="push_restock"
                       {% if user.profile.push_restock %}checked{% endif %}>
                <label class="form-check-label" for="pushRestock">
                  Back in Stock Alerts
                </label>
              </div>
              
              <div class="form-check mb-2">
                <input class="form-check-input" type="checkbox" id="pushReviews" name="push_reviews"
                       {% if user.profile.push_reviews %}checked{% endif %}>
                <label class="form-check-label" for="pushReviews">
                  Review Reminders
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column -->
      <div class="col-lg-4">
        <!-- Profile Picture -->
        <div class="form-section">
          <h5 class="section-title">
            <i class="fas fa-camera me-2"></i>Profile Picture
          </h5>
          
          <div class="text-center">
            <div class="avatar-upload">
              {% if user.profile.avatar %}
                <img src="{{ user.profile.avatar.url }}" class="avatar-preview" id="avatarPreview" alt="Profile Picture">
              {% else %}
                <div class="avatar-preview bg-light d-flex align-items-center justify-content-center" id="avatarPreview">
                  <i class="fas fa-user fa-3x text-muted"></i>
                </div>
              {% endif %}
              
              <div class="avatar-overlay" onclick="document.getElementById('avatarInput').click()">
                <i class="fas fa-camera fa-2x text-white"></i>
              </div>
            </div>
            
            <input type="file" id="avatarInput" name="avatar" accept="image/*" style="display: none;" onchange="previewAvatar(this)">
            
            <div class="mt-3">
              <button type="button" class="btn btn-outline-primary btn-sm" onclick="document.getElementById('avatarInput').click()">
                <i class="fas fa-upload me-1"></i>Upload Photo
              </button>
              {% if user.profile.avatar %}
                <button type="button" class="btn btn-outline-danger btn-sm ms-2" onclick="removeAvatar()">
                  <i class="fas fa-trash me-1"></i>Remove
                </button>
              {% endif %}
            </div>
            
            <small class="text-muted d-block mt-2">
              Recommended: Square image, at least 200x200px
            </small>
          </div>
        </div>

        <!-- Privacy Settings -->
        <div class="form-section">
          <h5 class="section-title">
            <i class="fas fa-shield-alt me-2"></i>Privacy Settings
          </h5>
          
          <div class="form-check mb-3">
            <input class="form-check-input" type="checkbox" id="publicProfile" name="public_profile"
                   {% if user.profile.public_profile %}checked{% endif %}>
            <label class="form-check-label" for="publicProfile">
              <strong>Make profile public</strong>
              <br><small class="text-muted">Others can see your reviews and activity</small>
            </label>
          </div>
          
          <div class="form-check mb-3">
            <input class="form-check-input" type="checkbox" id="showWishlist" name="show_wishlist"
                   {% if user.profile.show_wishlist %}checked{% endif %}>
            <label class="form-check-label" for="showWishlist">
              <strong>Show wishlist publicly</strong>
              <br><small class="text-muted">Others can see your wishlist items</small>
            </label>
          </div>
          
          <div class="form-check mb-3">
            <input class="form-check-input" type="checkbox" id="allowMessages" name="allow_messages"
                   {% if user.profile.allow_messages %}checked{% endif %}>
            <label class="form-check-label" for="allowMessages">
              <strong>Allow messages from other users</strong>
              <br><small class="text-muted">Users can send you private messages</small>
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- Save Actions -->
    <div class="save-actions">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <small class="text-muted">
            <i class="fas fa-info-circle me-1"></i>
            Changes are saved automatically as you type
          </small>
        </div>
        <div class="d-flex gap-2">
          <a href="/profile/" class="btn btn-outline-secondary">Cancel</a>
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-2"></i>Save Changes
          </button>
        </div>
      </div>
    </div>
  </form>
</div>

<script>
function togglePreference(id) {
  const checkbox = document.getElementById(id);
  const item = checkbox.closest('.preference-item');
  
  checkbox.checked = !checkbox.checked;
  
  if (checkbox.checked) {
    item.classList.add('selected');
  } else {
    item.classList.remove('selected');
  }
}

function previewAvatar(input) {
  if (input.files && input.files[0]) {
    const reader = new FileReader();
    
    reader.onload = function(e) {
      const preview = document.getElementById('avatarPreview');
      if (preview.tagName === 'IMG') {
        preview.src = e.target.result;
      } else {
        // Replace div with img
        const img = document.createElement('img');
        img.src = e.target.result;
        img.className = 'avatar-preview';
        img.id = 'avatarPreview';
        img.alt = 'Profile Picture';
        preview.parentNode.replaceChild(img, preview);
      }
    };
    
    reader.readAsDataURL(input.files[0]);
  }
}

function removeAvatar() {
  if (confirm('Are you sure you want to remove your profile picture?')) {
    // This would make an AJAX call to remove the avatar
    const preview = document.getElementById('avatarPreview');
    const div = document.createElement('div');
    div.className = 'avatar-preview bg-light d-flex align-items-center justify-content-center';
    div.id = 'avatarPreview';
    div.innerHTML = '<i class="fas fa-user fa-3x text-muted"></i>';
    preview.parentNode.replaceChild(div, preview);
    
    document.getElementById('avatarInput').value = '';
    showToast('Profile picture removed', 'success');
  }
}

// Initialize selected preferences
document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('.preference-item input[type="checkbox"]:checked').forEach(checkbox => {
    checkbox.closest('.preference-item').classList.add('selected');
  });
});

// Auto-save functionality
let saveTimeout;
document.querySelectorAll('input, textarea, select').forEach(input => {
  input.addEventListener('input', function() {
    clearTimeout(saveTimeout);
    saveTimeout = setTimeout(() => {
      // Auto-save logic would go here
      console.log('Auto-saving...');
    }, 2000);
  });
});

// Form submission
document.getElementById('editProfileForm').addEventListener('submit', function(e) {
  const submitBtn = this.querySelector('button[type="submit"]');
  const originalText = submitBtn.innerHTML;
  
  submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
  submitBtn.disabled = true;
  
  // Re-enable button after 3 seconds (in case of error)
  setTimeout(() => {
    submitBtn.innerHTML = originalText;
    submitBtn.disabled = false;
  }, 3000);
});

function showToast(message, type) {
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} position-fixed`;
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
  toast.innerHTML = `
    ${message}
    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
  `;
  document.body.appendChild(toast);
  
  setTimeout(() => {
    if (toast.parentElement) {
      toast.remove();
    }
  }, 3000);
}
</script>
{% endblock %}
