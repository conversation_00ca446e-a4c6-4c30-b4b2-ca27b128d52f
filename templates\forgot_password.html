{% extends 'base.html' %}
{% load static %}

{% block title %}Forgot Password - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="Reset your Best in Click password. Enter your email address and we'll send you a link to reset your password.">
<style>
  .forgot-container {
    min-height: 80vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  }
  .forgot-card {
    background: white;
    border-radius: 1.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
    max-width: 500px;
    margin: 0 auto;
  }
  .forgot-header {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    color: white;
    padding: 2rem;
    text-align: center;
  }
  .forgot-body {
    padding: 2rem;
  }
  .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }
  .success-message {
    background: #d1edff;
    border: 1px solid #0d6efd;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
  }
  .success-icon {
    width: 60px;
    height: 60px;
    background: #0d6efd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
  }
  .help-section {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-top: 2rem;
  }
  .help-item {
    display: flex;
    align-items: start;
    margin-bottom: 1rem;
  }
  .help-item:last-child {
    margin-bottom: 0;
  }
  .help-icon {
    width: 30px;
    height: 30px;
    background: #0d6efd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    margin-right: 1rem;
    flex-shrink: 0;
  }
  .back-to-login {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
  }
  .resend-timer {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 1rem;
  }
</style>
{% endblock %}

{% block content %}
<div class="forgot-container">
  <div class="container">
    <div class="forgot-card">
      <!-- Header -->
      <div class="forgot-header">
        <i class="fas fa-key fa-3x mb-3" style="opacity: 0.8;"></i>
        <h3 class="fw-bold mb-2">Forgot Password?</h3>
        <p class="mb-0">No worries, we'll help you reset it</p>
      </div>
      
      <!-- Body -->
      <div class="forgot-body">
        <!-- Success Message (hidden by default) -->
        <div class="success-message" id="successMessage" style="display: none;">
          <div class="success-icon">
            <i class="fas fa-envelope"></i>
          </div>
          <h5 class="fw-bold mb-2">Check Your Email</h5>
          <p class="mb-3">We've sent a password reset link to your email address. Please check your inbox and follow the instructions.</p>
          <small class="text-muted">
            Didn't receive the email? Check your spam folder or 
            <a href="#" onclick="resendEmail()" id="resendLink">resend the email</a>
          </small>
          <div class="resend-timer" id="resendTimer" style="display: none;">
            You can resend the email in <span id="countdown">60</span> seconds
          </div>
        </div>

        <!-- Reset Form -->
        <div id="resetForm">
          <!-- Error Messages -->
          {% if messages %}
            {% for message in messages %}
              <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
              </div>
            {% endfor %}
          {% endif %}

          <div class="text-center mb-4">
            <h5 class="fw-bold">Reset Your Password</h5>
            <p class="text-muted">Enter your email address and we'll send you a link to reset your password</p>
          </div>

          <form method="post" id="forgotPasswordForm">
            {% csrf_token %}
            
            <div class="mb-3">
              <label for="email" class="form-label">Email Address</label>
              <input type="email" class="form-control form-control-lg" id="email" name="email" 
                     placeholder="Enter your email address" required>
              <div class="invalid-feedback">
                Please enter a valid email address.
              </div>
            </div>
            
            <button type="submit" class="btn btn-primary btn-lg w-100 mb-3">
              <i class="fas fa-paper-plane me-2"></i>Send Reset Link
            </button>
          </form>

          <!-- Help Section -->
          <div class="help-section">
            <h6 class="fw-bold mb-3">Need Help?</h6>
            
            <div class="help-item">
              <div class="help-icon">
                <i class="fas fa-question"></i>
              </div>
              <div>
                <strong>Can't remember your email?</strong><br>
                <small class="text-muted">Try the email addresses you commonly use for online accounts.</small>
              </div>
            </div>
            
            <div class="help-item">
              <div class="help-icon">
                <i class="fas fa-clock"></i>
              </div>
              <div>
                <strong>How long does it take?</strong><br>
                <small class="text-muted">Reset emails are usually delivered within a few minutes.</small>
              </div>
            </div>
            
            <div class="help-item">
              <div class="help-icon">
                <i class="fas fa-shield-alt"></i>
              </div>
              <div>
                <strong>Is this secure?</strong><br>
                <small class="text-muted">Yes, the reset link expires after 24 hours for your security.</small>
              </div>
            </div>
            
            <div class="help-item">
              <div class="help-icon">
                <i class="fas fa-envelope"></i>
              </div>
              <div>
                <strong>Still not receiving emails?</strong><br>
                <small class="text-muted">
                  <a href="/contact/" class="text-decoration-none">Contact our support team</a> for assistance.
                </small>
              </div>
            </div>
          </div>
        </div>

        <!-- Back to Login -->
        <div class="back-to-login">
          <p class="text-muted mb-2">Remember your password?</p>
          <a href="/login/" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-2"></i>Back to Login
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
let resendCountdown = 60;
let countdownInterval;

// Form submission
document.getElementById('forgotPasswordForm').addEventListener('submit', function(e) {
  const email = document.getElementById('email');
  
  // Reset validation
  email.classList.remove('is-invalid');
  
  // Validate email
  if (!email.value || !isValidEmail(email.value)) {
    email.classList.add('is-invalid');
    e.preventDefault();
    showToast('Please enter a valid email address', 'danger');
    return;
  }
  
  // Show loading state
  const submitBtn = this.querySelector('button[type="submit"]');
  const originalText = submitBtn.innerHTML;
  submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
  submitBtn.disabled = true;
  
  // Simulate API call
  setTimeout(() => {
    // Hide form and show success message
    document.getElementById('resetForm').style.display = 'none';
    document.getElementById('successMessage').style.display = 'block';
    
    // Start resend countdown
    startResendCountdown();
    
    // Reset button (in case of error)
    submitBtn.innerHTML = originalText;
    submitBtn.disabled = false;
  }, 2000);
  
  // Prevent actual form submission for demo
  e.preventDefault();
});

function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function resendEmail() {
  if (resendCountdown > 0) {
    showToast(`Please wait ${resendCountdown} seconds before resending`, 'warning');
    return;
  }
  
  console.log('Resending email...');
  showToast('Reset email sent again!', 'success');
  
  // Restart countdown
  startResendCountdown();
}

function startResendCountdown() {
  resendCountdown = 60;
  document.getElementById('resendLink').style.display = 'none';
  document.getElementById('resendTimer').style.display = 'block';
  
  countdownInterval = setInterval(() => {
    resendCountdown--;
    document.getElementById('countdown').textContent = resendCountdown;
    
    if (resendCountdown <= 0) {
      clearInterval(countdownInterval);
      document.getElementById('resendTimer').style.display = 'none';
      document.getElementById('resendLink').style.display = 'inline';
    }
  }, 1000);
}

function showToast(message, type) {
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} position-fixed`;
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
  toast.innerHTML = `
    ${message}
    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
  `;
  document.body.appendChild(toast);
  
  setTimeout(() => {
    if (toast.parentElement) {
      toast.remove();
    }
  }, 3000);
}

// Auto-focus on email field
document.addEventListener('DOMContentLoaded', function() {
  document.getElementById('email').focus();
});

// Handle Enter key
document.getElementById('email').addEventListener('keypress', function(e) {
  if (e.key === 'Enter') {
    document.getElementById('forgotPasswordForm').dispatchEvent(new Event('submit'));
  }
});

// Demo email suggestion
document.addEventListener('DOMContentLoaded', function() {
  // Add demo email button for testing
  const demoBtn = document.createElement('button');
  demoBtn.type = 'button';
  demoBtn.className = 'btn btn-outline-secondary btn-sm w-100 mt-2';
  demoBtn.innerHTML = '<i class="fas fa-user me-1"></i>Use Demo Email';
  demoBtn.onclick = function() {
    document.getElementById('email').value = '<EMAIL>';
    showToast('Demo email filled!', 'info');
  };
  
  // Insert after the submit button
  const submitBtn = document.querySelector('button[type="submit"]');
  submitBtn.parentNode.insertBefore(demoBtn, submitBtn.nextSibling);
});
</script>
{% endblock %}
