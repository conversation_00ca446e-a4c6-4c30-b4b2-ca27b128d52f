{% extends 'base.html' %}
{% load static %}

{% block title %}Compare Products - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="Compare products side by side to make informed purchasing decisions. Compare prices, features, ratings, and specifications.">
<style>
  .compare-hero {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
  }
  .compare-table {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
  }
  .product-column {
    min-width: 250px;
    border-right: 1px solid #dee2e6;
  }
  .product-column:last-child {
    border-right: none;
  }
  .product-header {
    background: #f8f9fa;
    padding: 1.5rem;
    text-align: center;
    border-bottom: 2px solid #dee2e6;
  }
  .product-image {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 0.5rem;
    margin: 0 auto 1rem;
  }
  .product-name {
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
  }
  .product-price {
    font-size: 1.5rem;
    font-weight: bold;
    color: #0d6efd;
  }
  .compare-row {
    border-bottom: 1px solid #f8f9fa;
  }
  .compare-row:last-child {
    border-bottom: none;
  }
  .feature-label {
    background: #f8f9fa;
    font-weight: 600;
    padding: 1rem;
    border-right: 1px solid #dee2e6;
    min-width: 200px;
  }
  .feature-value {
    padding: 1rem;
    text-align: center;
  }
  .rating-stars {
    color: #ffc107;
  }
  .add-product-card {
    border: 2px dashed #dee2e6;
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
  }
  .add-product-card:hover {
    border-color: #0d6efd;
    background: #e3f0ff;
  }
  .comparison-actions {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  .winner-badge {
    background: #28a745;
    color: white;
    border-radius: 2rem;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: bold;
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
  }
  .better-value {
    background: #d4edda;
    border-left: 4px solid #28a745;
  }
  .worse-value {
    background: #f8d7da;
    border-left: 4px solid #dc3545;
  }
  .empty-comparison {
    text-align: center;
    padding: 3rem 0;
  }
  .product-suggestions {
    background: #fff3cd;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  .suggestion-item {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  .suggestion-item:hover {
    border-color: #0d6efd;
    background: #e3f0ff;
  }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="compare-hero">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-8">
        <h1 class="display-5 fw-bold mb-3">Compare Products</h1>
        <p class="lead mb-0">Make informed decisions by comparing products side by side</p>
      </div>
      <div class="col-lg-4 text-center">
        <i class="fas fa-balance-scale" style="font-size: 4rem; opacity: 0.3;"></i>
      </div>
    </div>
  </div>
</div>

<div class="container">
  <!-- Comparison Actions -->
  <div class="comparison-actions">
    <div class="row align-items-center">
      <div class="col-md-6">
        <h5 class="mb-2">
          <i class="fas fa-balance-scale me-2"></i>
          Comparing {{ products|length }} product{{ products|length|pluralize }}
        </h5>
        <p class="text-muted mb-0">Add up to 4 products to compare their features and prices</p>
      </div>
      <div class="col-md-6 text-end">
        <div class="btn-group" role="group">
          <button class="btn btn-outline-primary" onclick="clearComparison()">
            <i class="fas fa-trash me-1"></i>Clear All
          </button>
          <button class="btn btn-outline-primary" onclick="shareComparison()">
            <i class="fas fa-share-alt me-1"></i>Share
          </button>
          <button class="btn btn-primary" onclick="exportComparison()">
            <i class="fas fa-download me-1"></i>Export
          </button>
        </div>
      </div>
    </div>
  </div>

  {% if products %}
    <!-- Comparison Table -->
    <div class="compare-table">
      <div class="table-responsive">
        <table class="table table-borderless mb-0">
          <!-- Product Headers -->
          <thead>
            <tr>
              <th class="feature-label">Products</th>
              {% for product in products %}
                <th class="product-column">
                  <div class="product-header position-relative">
                    {% if forloop.first %}
                      <span class="winner-badge">Best Value</span>
                    {% endif %}
                    
                    <button class="btn btn-sm btn-outline-danger position-absolute top-0 start-0 m-2" 
                            onclick="removeProduct({{ product.id }})">
                      <i class="fas fa-times"></i>
                    </button>
                    
                    {% if product.image_url %}
                      <img src="{{ product.image_url }}" class="product-image d-block" alt="{{ product.name }}">
                    {% else %}
                      <div class="product-image bg-light d-flex align-items-center justify-content-center mx-auto">
                        <i class="fas fa-image fa-2x text-muted"></i>
                      </div>
                    {% endif %}
                    
                    <div class="product-name">{{ product.name|truncatechars:40 }}</div>
                    <div class="product-price">${{ product.price }}</div>
                    
                    <div class="mt-2">
                      <a href="/products/{{ product.id }}/" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>View Details
                      </a>
                    </div>
                  </div>
                </th>
              {% endfor %}
              
              <!-- Add Product Column -->
              {% if products|length < 4 %}
                <th class="product-column">
                  <div class="add-product-card" onclick="showAddProductModal()">
                    <i class="fas fa-plus fa-3x text-muted mb-3"></i>
                    <h6>Add Product</h6>
                    <p class="text-muted small mb-0">Compare up to 4 products</p>
                  </div>
                </th>
              {% endif %}
            </tr>
          </thead>
          
          <tbody>
            <!-- Price Comparison -->
            <tr class="compare-row">
              <td class="feature-label">
                <i class="fas fa-dollar-sign me-2"></i>Price
              </td>
              {% for product in products %}
                <td class="feature-value {% if product.is_best_price %}better-value{% endif %}">
                  <strong>${{ product.price }}</strong>
                  {% if product.original_price and product.original_price != product.price %}
                    <br><small class="text-muted text-decoration-line-through">${{ product.original_price }}</small>
                    <br><span class="badge bg-success">{{ product.discount_percentage }}% OFF</span>
                  {% endif %}
                </td>
              {% endfor %}
              {% if products|length < 4 %}<td class="feature-value">-</td>{% endif %}
            </tr>
            
            <!-- Rating Comparison -->
            <tr class="compare-row">
              <td class="feature-label">
                <i class="fas fa-star me-2"></i>Rating
              </td>
              {% for product in products %}
                <td class="feature-value">
                  <div class="rating-stars">
                    {% for i in "12345" %}
                      {% if forloop.counter <= product.rating %}★{% else %}☆{% endif %}
                    {% endfor %}
                  </div>
                  <small class="text-muted">{{ product.rating }}/5 ({{ product.review_count }} reviews)</small>
                </td>
              {% endfor %}
              {% if products|length < 4 %}<td class="feature-value">-</td>{% endif %}
            </tr>
            
            <!-- Brand Comparison -->
            <tr class="compare-row">
              <td class="feature-label">
                <i class="fas fa-tag me-2"></i>Brand
              </td>
              {% for product in products %}
                <td class="feature-value">
                  {% if product.brand %}
                    <strong>{{ product.brand.name }}</strong>
                  {% else %}
                    <span class="text-muted">Not specified</span>
                  {% endif %}
                </td>
              {% endfor %}
              {% if products|length < 4 %}<td class="feature-value">-</td>{% endif %}
            </tr>
            
            <!-- Category Comparison -->
            <tr class="compare-row">
              <td class="feature-label">
                <i class="fas fa-folder me-2"></i>Category
              </td>
              {% for product in products %}
                <td class="feature-value">
                  {% if product.category %}
                    {{ product.category.name }}
                  {% else %}
                    <span class="text-muted">Not specified</span>
                  {% endif %}
                </td>
              {% endfor %}
              {% if products|length < 4 %}<td class="feature-value">-</td>{% endif %}
            </tr>
            
            <!-- Availability Comparison -->
            <tr class="compare-row">
              <td class="feature-label">
                <i class="fas fa-check-circle me-2"></i>Availability
              </td>
              {% for product in products %}
                <td class="feature-value">
                  {% if product.is_available %}
                    <span class="badge bg-success">In Stock</span>
                    {% if product.stock_quantity %}
                      <br><small class="text-muted">{{ product.stock_quantity }} units</small>
                    {% endif %}
                  {% else %}
                    <span class="badge bg-danger">Out of Stock</span>
                  {% endif %}
                </td>
              {% endfor %}
              {% if products|length < 4 %}<td class="feature-value">-</td>{% endif %}
            </tr>
            
            <!-- Store Comparison -->
            <tr class="compare-row">
              <td class="feature-label">
                <i class="fas fa-store me-2"></i>Store
              </td>
              {% for product in products %}
                <td class="feature-value">
                  {% if product.shop %}
                    <strong>{{ product.shop.name }}</strong>
                    <br>
                    <div class="rating-stars small">
                      {% for i in "12345" %}
                        {% if forloop.counter <= product.shop.rating %}★{% else %}☆{% endif %}
                      {% endfor %}
                    </div>
                    <small class="text-muted">{{ product.shop.rating }}/5</small>
                  {% else %}
                    <span class="text-muted">Not specified</span>
                  {% endif %}
                </td>
              {% endfor %}
              {% if products|length < 4 %}<td class="feature-value">-</td>{% endif %}
            </tr>
            
            <!-- Specifications would be dynamically loaded here -->
            {% for spec in common_specifications %}
            <tr class="compare-row">
              <td class="feature-label">
                <i class="fas fa-cog me-2"></i>{{ spec.name }}
              </td>
              {% for product in products %}
                <td class="feature-value">
                  {{ product.specifications|get_item:spec.name|default:"-" }}
                </td>
              {% endfor %}
              {% if products|length < 4 %}<td class="feature-value">-</td>{% endif %}
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Comparison Summary -->
    <div class="row mt-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">
              <i class="fas fa-trophy text-warning me-2"></i>Best Overall
            </h5>
            <div class="d-flex align-items-center">
              {% if best_product.image_url %}
                <img src="{{ best_product.image_url }}" class="me-3" style="width: 60px; height: 60px; object-fit: cover; border-radius: 0.5rem;" alt="{{ best_product.name }}">
              {% endif %}
              <div>
                <h6 class="mb-1">{{ best_product.name }}</h6>
                <p class="text-muted mb-0">${{ best_product.price }} • {{ best_product.rating }}/5 stars</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">
              <i class="fas fa-dollar-sign text-success me-2"></i>Best Value
            </h5>
            <div class="d-flex align-items-center">
              {% if best_value.image_url %}
                <img src="{{ best_value.image_url }}" class="me-3" style="width: 60px; height: 60px; object-fit: cover; border-radius: 0.5rem;" alt="{{ best_value.name }}">
              {% endif %}
              <div>
                <h6 class="mb-1">{{ best_value.name }}</h6>
                <p class="text-muted mb-0">${{ best_value.price }} • Best price-to-quality ratio</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  {% else %}
    <!-- Empty State -->
    <div class="empty-comparison">
      <i class="fas fa-balance-scale fa-5x text-muted mb-4"></i>
      <h3>No Products to Compare</h3>
      <p class="text-muted mb-4">Add products to start comparing their features, prices, and specifications</p>
      <button class="btn btn-primary btn-lg" onclick="showAddProductModal()">
        <i class="fas fa-plus me-2"></i>Add Your First Product
      </button>
    </div>

    <!-- Product Suggestions -->
    <div class="product-suggestions">
      <h5 class="fw-bold mb-3">
        <i class="fas fa-lightbulb me-2"></i>Popular Comparisons
      </h5>
      <div class="row">
        <div class="col-md-4 mb-3">
          <div class="suggestion-item" onclick="addSuggestedComparison('smartphones')">
            <h6>Smartphones</h6>
            <p class="text-muted small mb-0">Compare latest iPhone vs Samsung Galaxy vs Google Pixel</p>
          </div>
        </div>
        <div class="col-md-4 mb-3">
          <div class="suggestion-item" onclick="addSuggestedComparison('laptops')">
            <h6>Laptops</h6>
            <p class="text-muted small mb-0">Compare MacBook vs ThinkPad vs Dell XPS</p>
          </div>
        </div>
        <div class="col-md-4 mb-3">
          <div class="suggestion-item" onclick="addSuggestedComparison('headphones')">
            <h6>Headphones</h6>
            <p class="text-muted small mb-0">Compare Sony vs Bose vs Apple AirPods</p>
          </div>
        </div>
      </div>
    </div>
  {% endif %}
</div>

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Add Product to Compare</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <input type="text" class="form-control" id="productSearchInput" placeholder="Search for products to compare..." onkeyup="searchProducts(this.value)">
        </div>
        <div id="productSearchResults">
          <!-- Search results will be loaded here -->
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function showAddProductModal() {
  const modal = new bootstrap.Modal(document.getElementById('addProductModal'));
  modal.show();
}

function searchProducts(query) {
  if (query.length < 2) {
    document.getElementById('productSearchResults').innerHTML = '';
    return;
  }
  
  // Simulate product search
  const results = [
    { id: 1, name: 'iPhone 15 Pro', price: 999, image: '/static/images/iphone.jpg' },
    { id: 2, name: 'Samsung Galaxy S24', price: 899, image: '/static/images/samsung.jpg' },
    { id: 3, name: 'Google Pixel 8', price: 699, image: '/static/images/pixel.jpg' }
  ].filter(p => p.name.toLowerCase().includes(query.toLowerCase()));
  
  const resultsHtml = results.map(product => `
    <div class="d-flex align-items-center p-3 border-bottom cursor-pointer" onclick="addProductToComparison(${product.id})">
      <img src="${product.image}" class="me-3" style="width: 50px; height: 50px; object-fit: cover; border-radius: 0.5rem;" alt="${product.name}">
      <div class="flex-grow-1">
        <h6 class="mb-1">${product.name}</h6>
        <p class="text-muted mb-0">$${product.price}</p>
      </div>
      <button class="btn btn-outline-primary btn-sm">
        <i class="fas fa-plus"></i>
      </button>
    </div>
  `).join('');
  
  document.getElementById('productSearchResults').innerHTML = resultsHtml;
}

function addProductToComparison(productId) {
  console.log('Adding product to comparison:', productId);
  // Add product to comparison and reload page
  const url = new URL(window.location);
  const currentProducts = url.searchParams.get('products') || '';
  const products = currentProducts ? currentProducts.split(',') : [];
  
  if (!products.includes(productId.toString()) && products.length < 4) {
    products.push(productId.toString());
    url.searchParams.set('products', products.join(','));
    window.location.href = url.toString();
  }
}

function removeProduct(productId) {
  const url = new URL(window.location);
  const currentProducts = url.searchParams.get('products') || '';
  const products = currentProducts.split(',').filter(id => id !== productId.toString());
  
  if (products.length > 0) {
    url.searchParams.set('products', products.join(','));
  } else {
    url.searchParams.delete('products');
  }
  
  window.location.href = url.toString();
}

function clearComparison() {
  if (confirm('Are you sure you want to clear all products from comparison?')) {
    const url = new URL(window.location);
    url.searchParams.delete('products');
    window.location.href = url.toString();
  }
}

function shareComparison() {
  if (navigator.share) {
    navigator.share({
      title: 'Product Comparison - Best in Click',
      text: 'Check out this product comparison',
      url: window.location.href
    });
  } else {
    navigator.clipboard.writeText(window.location.href);
    showToast('Comparison link copied to clipboard!', 'success');
  }
}

function exportComparison() {
  // Export comparison as PDF or image
  alert('Export functionality would be implemented here');
}

function addSuggestedComparison(category) {
  // Add suggested products for comparison
  console.log('Adding suggested comparison for:', category);
  alert(`Loading ${category} comparison...`);
}

function showToast(message, type) {
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} position-fixed`;
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
  toast.innerHTML = `
    ${message}
    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
  `;
  document.body.appendChild(toast);
  
  setTimeout(() => {
    if (toast.parentElement) {
      toast.remove();
    }
  }, 3000);
}

// Load comparison from URL parameters
document.addEventListener('DOMContentLoaded', function() {
  const urlParams = new URLSearchParams(window.location.search);
  const products = urlParams.get('products');
  
  if (products) {
    console.log('Loading comparison for products:', products);
    // Load and display comparison data
  }
});
</script>
{% endblock %}
