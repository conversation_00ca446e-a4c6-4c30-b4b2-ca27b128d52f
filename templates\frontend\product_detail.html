{% extends 'frontend/base.html' %}
{% load static %}

{% block title %}{{ product.name }} - E-Commerce Hub{% endblock %}

{% block meta_description %}{{ product.description|truncatewords:30 }}{% endblock %}

{% block extra_css %}
<style>
    .product-gallery {
        position: sticky;
        top: 2rem;
    }

    .main-image {
        width: 100%;
        height: 400px;
        object-fit: cover;
        border-radius: var(--border-radius);
        border: 1px solid var(--border-color);
    }

    .thumbnail-images {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
        overflow-x: auto;
    }

    .thumbnail {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: var(--border-radius);
        border: 2px solid transparent;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .thumbnail:hover,
    .thumbnail.active {
        border-color: var(--primary-color);
    }

    .product-info {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-sm);
    }

    .product-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: var(--text-primary);
    }

    .product-price {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 1rem;
    }

    .original-price {
        font-size: 1.5rem;
        color: var(--text-secondary);
        text-decoration: line-through;
        margin-left: 1rem;
    }

    .discount-badge {
        background: var(--danger-color);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        margin-left: 1rem;
    }

    .rating-section {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin: 1.5rem 0;
        padding: 1rem;
        background: var(--light-bg);
        border-radius: var(--border-radius);
    }

    .rating-stars {
        font-size: 1.5rem;
        color: #fbbf24;
    }

    .rating-text {
        font-size: 1.1rem;
        font-weight: 600;
    }

    .ai-rating-badge {
        background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .store-info {
        background: var(--light-bg);
        padding: 1rem;
        border-radius: var(--border-radius);
        margin: 1.5rem 0;
    }

    .reliability-score {
        background: var(--success-color);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .price-comparison {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow-sm);
        margin-top: 2rem;
    }

    .price-comparison-item {
        display: flex;
        justify-content: between;
        align-items: center;
        padding: 1rem;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        margin-bottom: 0.5rem;
        transition: all 0.2s ease;
    }

    .price-comparison-item:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
    }

    .best-price {
        border-color: var(--success-color);
        background: rgba(16, 185, 129, 0.1);
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        margin: 2rem 0;
    }

    .btn-large {
        padding: 1rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: var(--border-radius);
    }

    .tabs-section {
        margin-top: 3rem;
    }

    .nav-tabs {
        border-bottom: 2px solid var(--border-color);
    }

    .nav-tabs .nav-link {
        border: none;
        color: var(--text-secondary);
        font-weight: 600;
        padding: 1rem 1.5rem;
    }

    .nav-tabs .nav-link.active {
        color: var(--primary-color);
        border-bottom: 2px solid var(--primary-color);
        background: none;
    }

    .tab-content {
        padding: 2rem 0;
    }

    .review-item {
        border-bottom: 1px solid var(--border-color);
        padding: 1.5rem 0;
    }

    .review-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .reviewer-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .verified-badge {
        background: var(--success-color);
        color: white;
        padding: 0.2rem 0.5rem;
        border-radius: 10px;
        font-size: 0.7rem;
    }

    .sentiment-badge {
        padding: 0.2rem 0.5rem;
        border-radius: 10px;
        font-size: 0.7rem;
        font-weight: 600;
    }

    .sentiment-positive {
        background: rgba(16, 185, 129, 0.2);
        color: var(--success-color);
    }

    .sentiment-negative {
        background: rgba(239, 68, 68, 0.2);
        color: var(--danger-color);
    }

    .sentiment-neutral {
        background: rgba(100, 116, 139, 0.2);
        color: var(--secondary-color);
    }

    .similar-products {
        margin-top: 3rem;
    }

    .similar-product-card {
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
    }

    .similar-product-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .similar-product-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }

    .price-trend-chart {
        height: 300px;
        margin: 1rem 0;
    }

    .ai-insights {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: var(--border-radius);
        margin: 1.5rem 0;
    }

    .insight-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .insight-score {
        background: rgba(255, 255, 255, 0.2);
        padding: 0.25rem 0.5rem;
        border-radius: 10px;
        font-weight: 600;
    }

    @media (max-width: 768px) {
        .product-title {
            font-size: 1.5rem;
        }
        
        .product-price {
            font-size: 2rem;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .btn-large {
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
            <li class="breadcrumb-item"><a href="{% url 'products' %}">Products</a></li>
            {% if product.category %}
                <li class="breadcrumb-item">
                    <a href="{% url 'products' %}?category_id={{ product.category.id }}">
                        {{ product.category.name }}
                    </a>
                </li>
            {% endif %}
            <li class="breadcrumb-item active" aria-current="page">{{ product.name|truncatewords:5 }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Product Gallery -->
        <div class="col-lg-5">
            <div class="product-gallery">
                <img id="mainImage" 
                     src="{{ product.image_url|default:'/static/images/product-placeholder.jpg' }}" 
                     alt="{{ product.name }}" 
                     class="main-image">
                
                <div class="thumbnail-images">
                    <img src="{{ product.image_url|default:'/static/images/product-placeholder.jpg' }}" 
                         alt="{{ product.name }}" 
                         class="thumbnail active"
                         onclick="changeMainImage(this.src)">
                    <!-- Additional thumbnails would be loaded here -->
                </div>
            </div>
        </div>

        <!-- Product Information -->
        <div class="col-lg-7">
            <div class="product-info">
                <h1 class="product-title" id="productTitle">Loading...</h1>
                
                <div class="rating-section" id="ratingSection">
                    <div class="spinner-border text-primary" role="status"></div>
                </div>

                <div class="price-section" id="priceSection">
                    <div class="spinner-border text-primary" role="status"></div>
                </div>

                <div class="store-info" id="storeInfo">
                    <div class="spinner-border text-primary" role="status"></div>
                </div>

                <div class="ai-insights" id="aiInsights" style="display: none;">
                    <h5><i class="fas fa-brain me-2"></i>AI Product Analysis</h5>
                    <div id="aiInsightsList"></div>
                </div>

                <div class="action-buttons">
                    <button id="addToCompareBtn" class="btn btn-outline-primary btn-large">
                        <i class="fas fa-balance-scale me-2"></i>Add to Compare
                    </button>
                    <button id="saveProductBtn" class="btn btn-outline-success btn-large">
                        <i class="fas fa-heart me-2"></i>Save Product
                    </button>
                    <button id="shareProductBtn" class="btn btn-outline-info btn-large">
                        <i class="fas fa-share me-2"></i>Share
                    </button>
                </div>

                <div class="description-section" id="descriptionSection">
                    <h5>Description</h5>
                    <div id="productDescription">Loading...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Price Comparison Section -->
    <div class="price-comparison" id="priceComparison" style="display: none;">
        <h4><i class="fas fa-tags me-2"></i>Price Comparison</h4>
        <div id="priceComparisonList">
            <div class="text-center">
                <div class="spinner-border text-primary" role="status"></div>
            </div>
        </div>
    </div>

    <!-- Tabs Section -->
    <div class="tabs-section">
        <ul class="nav nav-tabs" id="productTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="reviews-tab" data-bs-toggle="tab" 
                        data-bs-target="#reviews" type="button" role="tab">
                    Reviews
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="specifications-tab" data-bs-toggle="tab" 
                        data-bs-target="#specifications" type="button" role="tab">
                    Specifications
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="price-history-tab" data-bs-toggle="tab" 
                        data-bs-target="#price-history" type="button" role="tab">
                    Price History
                </button>
            </li>
        </ul>
        
        <div class="tab-content" id="productTabContent">
            <!-- Reviews Tab -->
            <div class="tab-pane fade show active" id="reviews" role="tabpanel">
                <div class="row">
                    <div class="col-md-8">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>Customer Reviews</h5>
                            {% if user.is_authenticated %}
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#reviewModal">
                                    Write a Review
                                </button>
                            {% endif %}
                        </div>
                        
                        <div id="reviewsList">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6>Review Summary</h6>
                            </div>
                            <div class="card-body" id="reviewSummary">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Specifications Tab -->
            <div class="tab-pane fade" id="specifications" role="tabpanel">
                <div id="productSpecifications">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status"></div>
                    </div>
                </div>
            </div>
            
            <!-- Price History Tab -->
            <div class="tab-pane fade" id="price-history" role="tabpanel">
                <div class="price-trend-chart">
                    <canvas id="priceChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Similar Products Section -->
    <div class="similar-products">
        <h4><i class="fas fa-lightbulb me-2"></i>Similar Products</h4>
        <div id="similarProducts" class="row g-4">
            <div class="col-12 text-center">
                <div class="spinner-border text-primary" role="status"></div>
            </div>
        </div>
    </div>
</div>

<!-- Review Modal -->
{% if user.is_authenticated %}
<div class="modal fade" id="reviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Write a Review</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="reviewForm">
                    <div class="mb-3">
                        <label for="reviewRating" class="form-label">Rating *</label>
                        <div class="rating-input" id="ratingInput">
                            <i class="fas fa-star" data-rating="1"></i>
                            <i class="fas fa-star" data-rating="2"></i>
                            <i class="fas fa-star" data-rating="3"></i>
                            <i class="fas fa-star" data-rating="4"></i>
                            <i class="fas fa-star" data-rating="5"></i>
                        </div>
                        <input type="hidden" id="reviewRating" name="rating" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reviewTitle" class="form-label">Title</label>
                        <input type="text" class="form-control" id="reviewTitle" name="title" 
                               placeholder="Summarize your review">
                    </div>
                    
                    <div class="mb-3">
                        <label for="reviewComment" class="form-label">Review</label>
                        <textarea class="form-control" id="reviewComment" name="comment" rows="4" 
                                  placeholder="Share your experience with this product"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="reviewPros" class="form-label">Pros</label>
                                <textarea class="form-control" id="reviewPros" name="pros" rows="3" 
                                          placeholder="What did you like?"></textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="reviewCons" class="form-label">Cons</label>
                                <textarea class="form-control" id="reviewCons" name="cons" rows="3" 
                                          placeholder="What could be improved?"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="submitReview">Submit Review</button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
let productId = '{{ product.id }}';
let productData = null;
let priceChart = null;

document.addEventListener('DOMContentLoaded', function() {
    loadProductDetails();
    setupEventListeners();
    {% if user.is_authenticated %}
    setupReviewForm();
    {% endif %}
});

function loadProductDetails() {
    fetch(`${window.API_BASE_URL}product_details/?product_id=${productId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                productData = data.product;
                renderProductDetails(productData);
                loadSimilarProducts();
                loadPriceHistory();
            } else {
                utils.showAlert('Failed to load product details', 'error');
            }
        })
        .catch(error => {
            console.error('Error loading product details:', error);
            utils.showAlert('Failed to load product details', 'error');
        });
}

function renderProductDetails(product) {
    // Update title
    document.getElementById('productTitle').textContent = product.name;
    document.title = `${product.name} - E-Commerce Hub`;
    
    // Update rating section
    const ratingSection = document.getElementById('ratingSection');
    ratingSection.innerHTML = `
        <div class="rating-stars">
            ${'★'.repeat(Math.floor(product.rating))}${'☆'.repeat(5-Math.floor(product.rating))}
        </div>
        <span class="rating-text">${product.rating}/5</span>
        <span class="text-muted">(${product.review_summary.total_reviews} reviews)</span>
        ${product.ai_rating ? 
            `<span class="ai-rating-badge">
                <i class="fas fa-brain me-1"></i>AI Score: ${product.ai_rating.overall_rating}/5
            </span>` : ''}
    `;
    
    // Update price section
    const priceSection = document.getElementById('priceSection');
    priceSection.innerHTML = `
        <div class="d-flex align-items-center">
            <span class="product-price">${utils.formatCurrency(product.price)}</span>
            ${product.original_price ? 
                `<span class="original-price">${utils.formatCurrency(product.original_price)}</span>
                 <span class="discount-badge">-${product.discount_percentage}%</span>` : ''}
        </div>
    `;
    
    // Update store info
    const storeInfo = document.getElementById('storeInfo');
    storeInfo.innerHTML = `
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h6><i class="fas fa-store me-2"></i>${product.shop.name}</h6>
                <small class="text-muted">Reliability Score: 
                    <span class="reliability-score">${product.shop.reliability_score}/5</span>
                </small>
            </div>
            <a href="/stores/${product.shop.id}/" class="btn btn-sm btn-outline-primary">
                View Store
            </a>
        </div>
    `;
    
    // Update description
    document.getElementById('productDescription').innerHTML = product.description || 'No description available.';
    
    // Show AI insights if available
    if (product.ai_rating && product.ai_rating.components) {
        renderAIInsights(product.ai_rating);
    }
    
    // Render price comparison
    if (product.price_comparison && product.price_comparison.length > 0) {
        renderPriceComparison(product.price_comparison);
    }
    
    // Render reviews
    renderReviews(product.recent_reviews);
    renderReviewSummary(product.review_summary);
}

function renderAIInsights(aiRating) {
    const aiInsights = document.getElementById('aiInsights');
    const insightsList = document.getElementById('aiInsightsList');
    
    let insights = [];
    Object.entries(aiRating.components).forEach(([component, score]) => {
        insights.push(`
            <div class="insight-item">
                <span>${component.replace('_', ' ').toUpperCase()}</span>
                <span class="insight-score">${score}/5</span>
            </div>
        `);
    });
    
    insightsList.innerHTML = insights.join('');
    aiInsights.style.display = 'block';
}

function renderPriceComparison(priceComparison) {
    const section = document.getElementById('priceComparison');
    const list = document.getElementById('priceComparisonList');
    
    // Add current store to comparison
    const allPrices = [
        {
            shop_name: productData.shop.name,
            price: productData.price,
            is_available: true,
            is_current: true
        },
        ...priceComparison
    ];
    
    // Sort by price
    allPrices.sort((a, b) => a.price - b.price);
    
    list.innerHTML = allPrices.map((item, index) => `
        <div class="price-comparison-item ${index === 0 ? 'best-price' : ''}">
            <div class="d-flex justify-content-between align-items-center w-100">
                <div>
                    <strong>${item.shop_name}</strong>
                    ${item.is_current ? '<span class="badge bg-primary ms-2">Current Store</span>' : ''}
                    ${index === 0 ? '<span class="badge bg-success ms-2">Best Price</span>' : ''}
                </div>
                <div class="text-end">
                    <div class="h5 mb-0">${utils.formatCurrency(item.price)}</div>
                    <small class="text-muted">
                        ${item.is_available ? 'In Stock' : 'Out of Stock'}
                    </small>
                </div>
            </div>
        </div>
    `).join('');
    
    section.style.display = 'block';
}

function renderReviews(reviews) {
    const reviewsList = document.getElementById('reviewsList');
    
    if (!reviews || reviews.length === 0) {
        reviewsList.innerHTML = '<p class="text-muted">No reviews yet. Be the first to review this product!</p>';
        return;
    }
    
    reviewsList.innerHTML = reviews.map(review => `
        <div class="review-item">
            <div class="review-header">
                <div class="reviewer-info">
                    <strong>${review.user_name}</strong>
                    ${review.verified_purchase ? '<span class="verified-badge">Verified Purchase</span>' : ''}
                    ${review.sentiment_label ? 
                        `<span class="sentiment-badge sentiment-${review.sentiment_label}">
                            ${review.sentiment_label.charAt(0).toUpperCase() + review.sentiment_label.slice(1)}
                        </span>` : ''}
                </div>
                <div class="text-end">
                    <div class="rating-stars">
                        ${'★'.repeat(review.rating)}${'☆'.repeat(5-review.rating)}
                    </div>
                    <small class="text-muted">${utils.formatDate(review.created_at)}</small>
                </div>
            </div>
            
            ${review.title ? `<h6>${review.title}</h6>` : ''}
            ${review.comment ? `<p>${review.comment}</p>` : ''}
            
            ${review.pros || review.cons ? `
                <div class="row mt-2">
                    ${review.pros ? `
                        <div class="col-md-6">
                            <strong class="text-success">Pros:</strong>
                            <p class="small">${review.pros}</p>
                        </div>
                    ` : ''}
                    ${review.cons ? `
                        <div class="col-md-6">
                            <strong class="text-danger">Cons:</strong>
                            <p class="small">${review.cons}</p>
                        </div>
                    ` : ''}
                </div>
            ` : ''}
        </div>
    `).join('');
}

function renderReviewSummary(summary) {
    const reviewSummary = document.getElementById('reviewSummary');
    
    reviewSummary.innerHTML = `
        <div class="text-center mb-3">
            <div class="h2">${summary.average_rating}</div>
            <div class="rating-stars mb-2">
                ${'★'.repeat(Math.floor(summary.average_rating))}${'☆'.repeat(5-Math.floor(summary.average_rating))}
            </div>
            <div class="text-muted">${summary.total_reviews} reviews</div>
        </div>
        
        ${Object.entries(summary.sentiment_distribution).length > 0 ? `
            <div class="sentiment-breakdown">
                <h6>Sentiment Breakdown</h6>
                ${Object.entries(summary.sentiment_distribution).map(([sentiment, count]) => `
                    <div class="d-flex justify-content-between">
                        <span class="sentiment-badge sentiment-${sentiment}">${sentiment}</span>
                        <span>${count}</span>
                    </div>
                `).join('')}
            </div>
        ` : ''}
    `;
}

function loadSimilarProducts() {
    fetch(`${window.API_BASE_URL}similar_products/?product_id=${productId}&limit=4`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.similar_products) {
                renderSimilarProducts(data.similar_products);
            }
        })
        .catch(error => {
            console.error('Error loading similar products:', error);
        });
}

function renderSimilarProducts(products) {
    const container = document.getElementById('similarProducts');
    
    if (!products || products.length === 0) {
        container.innerHTML = '<div class="col-12 text-center text-muted">No similar products found</div>';
        return;
    }
    
    container.innerHTML = products.map(product => `
        <div class="col-lg-3 col-md-6">
            <div class="similar-product-card">
                <img src="${product.image_url || '/static/images/product-placeholder.jpg'}" 
                     class="similar-product-image" 
                     alt="${product.name}">
                <div class="p-3">
                    <h6><a href="/products/${product.id}/" class="text-decoration-none">${product.name}</a></h6>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="fw-bold text-primary">${utils.formatCurrency(product.price)}</span>
                        <div class="rating-stars small">
                            ${'★'.repeat(Math.floor(product.rating))}${'☆'.repeat(5-Math.floor(product.rating))}
                        </div>
                    </div>
                    <small class="text-muted">${product.shop.name}</small>
                    <div class="mt-2">
                        <small class="text-success">
                            Similarity: ${Math.round(product.similarity_score * 100)}%
                        </small>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

function loadPriceHistory() {
    // This would typically load from the price trends API
    // For now, we'll create a simple chart
    const ctx = document.getElementById('priceChart').getContext('2d');
    
    // Sample data - in real implementation, this would come from the API
    const priceData = {
        labels: ['30 days ago', '25 days ago', '20 days ago', '15 days ago', '10 days ago', '5 days ago', 'Today'],
        datasets: [{
            label: 'Price',
            data: [120, 115, 118, 110, 105, 108, productData.price],
            borderColor: 'rgb(37, 99, 235)',
            backgroundColor: 'rgba(37, 99, 235, 0.1)',
            tension: 0.1
        }]
    };
    
    priceChart = new Chart(ctx, {
        type: 'line',
        data: priceData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return '$' + value;
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

function setupEventListeners() {
    // Add to compare
    document.getElementById('addToCompareBtn').addEventListener('click', function() {
        // Implementation for adding to compare
        utils.showAlert('Product added to comparison list', 'success');
    });
    
    // Save product
    document.getElementById('saveProductBtn').addEventListener('click', function() {
        if (!{{ user.is_authenticated|yesno:"true,false" }}) {
            utils.showAlert('Please login to save products', 'warning');
            return;
        }
        
        // Track engagement
        fetch(`${window.API_BASE_URL}track_engagement/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': window.CSRF_TOKEN
            },
            body: JSON.stringify({
                event_type: 'save_product',
                product_id: productId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                utils.showAlert('Product saved to your wishlist', 'success');
                this.innerHTML = '<i class="fas fa-heart me-2"></i>Saved';
                this.classList.remove('btn-outline-success');
                this.classList.add('btn-success');
            }
        });
    });
    
    // Share product
    document.getElementById('shareProductBtn').addEventListener('click', function() {
        if (navigator.share) {
            navigator.share({
                title: productData.name,
                text: productData.description,
                url: window.location.href
            });
        } else {
            // Fallback - copy to clipboard
            navigator.clipboard.writeText(window.location.href);
            utils.showAlert('Product link copied to clipboard', 'success');
        }
    });
}

{% if user.is_authenticated %}
function setupReviewForm() {
    // Rating input
    const ratingStars = document.querySelectorAll('#ratingInput i');
    let selectedRating = 0;
    
    ratingStars.forEach(star => {
        star.addEventListener('click', function() {
            selectedRating = parseInt(this.dataset.rating);
            document.getElementById('reviewRating').value = selectedRating;
            
            ratingStars.forEach((s, index) => {
                if (index < selectedRating) {
                    s.style.color = '#fbbf24';
                } else {
                    s.style.color = '#d1d5db';
                }
            });
        });
        
        star.addEventListener('mouseover', function() {
            const hoverRating = parseInt(this.dataset.rating);
            ratingStars.forEach((s, index) => {
                if (index < hoverRating) {
                    s.style.color = '#fbbf24';
                } else {
                    s.style.color = '#d1d5db';
                }
            });
        });
    });
    
    // Submit review
    document.getElementById('submitReview').addEventListener('click', function() {
        const formData = {
            product_id: productId,
            rating: document.getElementById('reviewRating').value,
            title: document.getElementById('reviewTitle').value,
            comment: document.getElementById('reviewComment').value,
            pros: document.getElementById('reviewPros').value,
            cons: document.getElementById('reviewCons').value
        };
        
        if (!formData.rating) {
            utils.showAlert('Please select a rating', 'warning');
            return;
        }
        
        utils.showLoading(this);
        
        fetch(`${window.API_BASE_URL}submit_review/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': window.CSRF_TOKEN
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            utils.hideLoading(this);
            if (data.success) {
                utils.showAlert('Review submitted successfully', 'success');
                bootstrap.Modal.getInstance(document.getElementById('reviewModal')).hide();
                // Reload product details to show new review
                loadProductDetails();
            } else {
                utils.showAlert(data.error || 'Failed to submit review', 'error');
            }
        })
        .catch(error => {
            utils.hideLoading(this);
            console.error('Error submitting review:', error);
            utils.showAlert('Failed to submit review', 'error');
        });
    });
}
{% endif %}

function changeMainImage(src) {
    document.getElementById('mainImage').src = src;
    
    // Update active thumbnail
    document.querySelectorAll('.thumbnail').forEach(thumb => {
        thumb.classList.remove('active');
    });
    event.target.classList.add('active');
}
</script>
{% endblock %}
