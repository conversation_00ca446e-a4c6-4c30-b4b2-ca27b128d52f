{% extends 'frontend/base.html' %}
{% load static %}

{% block title %}My Account - E-Commerce Hub{% endblock %}

{% block extra_css %}
<style>
    .dashboard-sidebar {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
        padding: 1.5rem;
        height: fit-content;
        position: sticky;
        top: 2rem;
    }

    .sidebar-nav {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .sidebar-nav li {
        margin-bottom: 0.5rem;
    }

    .sidebar-nav a {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        color: var(--text-secondary);
        text-decoration: none;
        border-radius: var(--border-radius);
        transition: all 0.2s ease;
    }

    .sidebar-nav a:hover,
    .sidebar-nav a.active {
        background: var(--primary-color);
        color: white;
    }

    .sidebar-nav i {
        width: 20px;
        margin-right: 0.75rem;
    }

    .dashboard-content {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
        padding: 2rem;
        min-height: 600px;
    }

    .welcome-section {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: white;
        padding: 2rem;
        border-radius: var(--border-radius);
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        display: block;
    }

    .stat-label {
        color: var(--text-secondary);
        font-size: 0.9rem;
    }

    .activity-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-bottom: 1px solid var(--border-color);
        transition: background-color 0.2s ease;
    }

    .activity-item:hover {
        background: var(--light-bg);
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.2rem;
    }

    .activity-icon.view {
        background: rgba(59, 130, 246, 0.1);
        color: #3b82f6;
    }

    .activity-icon.like {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
    }

    .activity-icon.review {
        background: rgba(16, 185, 129, 0.1);
        color: #10b981;
    }

    .activity-icon.save {
        background: rgba(245, 158, 11, 0.1);
        color: #f59e0b;
    }

    .recommendation-card {
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
    }

    .recommendation-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .recommendation-image {
        width: 100%;
        height: 180px;
        object-fit: cover;
    }

    .recommendation-badge {
        position: absolute;
        top: 10px;
        left: 10px;
        background: var(--primary-color);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .review-card {
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.2s ease;
    }

    .review-card:hover {
        box-shadow: var(--shadow-md);
    }

    .review-rating {
        color: #fbbf24;
        margin-bottom: 0.5rem;
    }

    .sentiment-badge {
        padding: 0.2rem 0.5rem;
        border-radius: 10px;
        font-size: 0.7rem;
        font-weight: 600;
    }

    .sentiment-positive {
        background: rgba(16, 185, 129, 0.2);
        color: var(--success-color);
    }

    .sentiment-negative {
        background: rgba(239, 68, 68, 0.2);
        color: var(--danger-color);
    }

    .sentiment-neutral {
        background: rgba(100, 116, 139, 0.2);
        color: var(--secondary-color);
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: var(--text-secondary);
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    @media (max-width: 768px) {
        .dashboard-sidebar {
            position: static;
            margin-bottom: 2rem;
        }
        
        .sidebar-nav {
            display: flex;
            overflow-x: auto;
            gap: 0.5rem;
        }
        
        .sidebar-nav li {
            margin-bottom: 0;
            white-space: nowrap;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-md-4">
            <div class="dashboard-sidebar">
                <div class="text-center mb-4">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                         style="width: 60px; height: 60px; font-size: 1.5rem;">
                        {{ user.username|first|upper }}
                    </div>
                    <h5 class="mt-2 mb-0">{{ user.username }}</h5>
                    <small class="text-muted">{{ user.email }}</small>
                </div>
                
                <ul class="sidebar-nav">
                    <li>
                        <a href="#overview" class="nav-link active" data-section="overview">
                            <i class="fas fa-tachometer-alt"></i>
                            Overview
                        </a>
                    </li>
                    <li>
                        <a href="#activity" class="nav-link" data-section="activity">
                            <i class="fas fa-history"></i>
                            Recent Activity
                        </a>
                    </li>
                    <li>
                        <a href="#reviews" class="nav-link" data-section="reviews">
                            <i class="fas fa-star"></i>
                            My Reviews
                        </a>
                    </li>
                    <li>
                        <a href="#recommendations" class="nav-link" data-section="recommendations">
                            <i class="fas fa-lightbulb"></i>
                            Recommendations
                        </a>
                    </li>
                    <li>
                        <a href="#wishlist" class="nav-link" data-section="wishlist">
                            <i class="fas fa-heart"></i>
                            Saved Items
                        </a>
                    </li>
                    <li>
                        <a href="#settings" class="nav-link" data-section="settings">
                            <i class="fas fa-cog"></i>
                            Settings
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-lg-9 col-md-8">
            <div class="dashboard-content">
                <!-- Overview Section -->
                <div id="overview-section" class="dashboard-section">
                    <div class="welcome-section">
                        <h2>Welcome back, {{ user.username }}!</h2>
                        <p class="mb-0">Here's what's happening with your account</p>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row g-4 mb-4">
                        <div class="col-lg-3 col-md-6">
                            <div class="stat-card">
                                <span id="totalReviews" class="stat-number">0</span>
                                <div class="stat-label">Reviews Written</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="stat-card">
                                <span id="totalLikes" class="stat-number">0</span>
                                <div class="stat-label">Products Liked</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="stat-card">
                                <span id="totalSaves" class="stat-number">0</span>
                                <div class="stat-label">Items Saved</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="stat-card">
                                <span id="totalComparisons" class="stat-number">0</span>
                                <div class="stat-label">Comparisons Made</div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-clock me-2"></i>Recent Activity</h5>
                                </div>
                                <div class="card-body" id="recentActivityPreview">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-lightbulb me-2"></i>Recommended for You</h5>
                                </div>
                                <div class="card-body" id="recommendationsPreview">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Activity Section -->
                <div id="activity-section" class="dashboard-section d-none">
                    <h3><i class="fas fa-history me-2"></i>Recent Activity</h3>
                    <div id="activityList">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                    </div>
                </div>

                <!-- Reviews Section -->
                <div id="reviews-section" class="dashboard-section d-none">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3><i class="fas fa-star me-2"></i>My Reviews</h3>
                        <a href="{% url 'products' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Write New Review
                        </a>
                    </div>
                    <div id="reviewsList">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                    </div>
                </div>

                <!-- Recommendations Section -->
                <div id="recommendations-section" class="dashboard-section d-none">
                    <h3><i class="fas fa-lightbulb me-2"></i>Personalized Recommendations</h3>
                    <p class="text-muted mb-4">Based on your browsing history and preferences</p>
                    <div id="recommendationsList" class="row g-4">
                        <div class="col-12 text-center">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                    </div>
                </div>

                <!-- Wishlist Section -->
                <div id="wishlist-section" class="dashboard-section d-none">
                    <h3><i class="fas fa-heart me-2"></i>Saved Items</h3>
                    <p class="text-muted mb-4">Products you've saved for later</p>
                    <div id="wishlistItems" class="row g-4">
                        <div class="col-12 text-center">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                    </div>
                </div>

                <!-- Settings Section -->
                <div id="settings-section" class="dashboard-section d-none">
                    <h3><i class="fas fa-cog me-2"></i>Account Settings</h3>
                    
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Profile Information</h5>
                                </div>
                                <div class="card-body">
                                    <form id="profileForm">
                                        <div class="mb-3">
                                            <label for="username" class="form-label">Username</label>
                                            <input type="text" class="form-control" id="username" 
                                                   value="{{ user.username }}" readonly>
                                        </div>
                                        <div class="mb-3">
                                            <label for="email" class="form-label">Email</label>
                                            <input type="email" class="form-control" id="email" 
                                                   value="{{ user.email }}">
                                        </div>
                                        <div class="mb-3">
                                            <label for="firstName" class="form-label">First Name</label>
                                            <input type="text" class="form-control" id="firstName" 
                                                   value="{{ user.first_name }}">
                                        </div>
                                        <div class="mb-3">
                                            <label for="lastName" class="form-label">Last Name</label>
                                            <input type="text" class="form-control" id="lastName" 
                                                   value="{{ user.last_name }}">
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            Update Profile
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Preferences</h5>
                                </div>
                                <div class="card-body">
                                    <form id="preferencesForm">
                                        <div class="mb-3">
                                            <label class="form-label">Email Notifications</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                       id="emailPriceAlerts" checked>
                                                <label class="form-check-label" for="emailPriceAlerts">
                                                    Price drop alerts
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                       id="emailRecommendations" checked>
                                                <label class="form-check-label" for="emailRecommendations">
                                                    Product recommendations
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                       id="emailNewsletter">
                                                <label class="form-check-label" for="emailNewsletter">
                                                    Newsletter and updates
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="currency" class="form-label">Preferred Currency</label>
                                            <select class="form-select" id="currency">
                                                <option value="USD" selected>USD ($)</option>
                                                <option value="EUR">EUR (€)</option>
                                                <option value="GBP">GBP (£)</option>
                                            </select>
                                        </div>

                                        <button type="submit" class="btn btn-primary">
                                            Save Preferences
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let dashboardData = null;

document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
    setupNavigation();
    setupForms();
});

function loadDashboardData() {
    fetch(`${window.API_BASE_URL}user_dashboard/`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                dashboardData = data;
                renderDashboardData(data);
            } else {
                utils.showAlert('Failed to load dashboard data', 'error');
            }
        })
        .catch(error => {
            console.error('Error loading dashboard data:', error);
            utils.showAlert('Failed to load dashboard data', 'error');
        });
}

function renderDashboardData(data) {
    // Update statistics
    document.getElementById('totalReviews').textContent = data.user_statistics.total_reviews;
    document.getElementById('totalLikes').textContent = data.user_statistics.total_likes;
    document.getElementById('totalSaves').textContent = data.user_statistics.total_saves;
    document.getElementById('totalComparisons').textContent = data.user_statistics.total_comparisons;
    
    // Render recent activity preview
    renderActivityPreview(data.recent_activity.slice(0, 3));
    
    // Render recommendations preview
    renderRecommendationsPreview(data.recommendations.slice(0, 2));
    
    // Render full activity list
    renderActivityList(data.recent_activity);
    
    // Render reviews list
    renderReviewsList(data.recent_reviews);
    
    // Render full recommendations
    renderRecommendationsList(data.recommendations);
}

function renderActivityPreview(activities) {
    const container = document.getElementById('recentActivityPreview');
    
    if (!activities || activities.length === 0) {
        container.innerHTML = '<p class="text-muted">No recent activity</p>';
        return;
    }
    
    container.innerHTML = activities.map(activity => `
        <div class="activity-item">
            <div class="activity-icon ${activity.event_type}">
                <i class="fas fa-${getActivityIcon(activity.event_type)}"></i>
            </div>
            <div class="flex-grow-1">
                <div class="fw-medium">${getActivityDescription(activity)}</div>
                <small class="text-muted">${utils.formatDate(activity.timestamp)}</small>
            </div>
        </div>
    `).join('');
}

function renderRecommendationsPreview(recommendations) {
    const container = document.getElementById('recommendationsPreview');
    
    if (!recommendations || recommendations.length === 0) {
        container.innerHTML = '<p class="text-muted">No recommendations available</p>';
        return;
    }
    
    container.innerHTML = recommendations.map(product => `
        <div class="d-flex align-items-center mb-3">
            <img src="${product.image_url || '/static/images/product-placeholder.jpg'}" 
                 class="rounded me-3" 
                 style="width: 50px; height: 50px; object-fit: cover;"
                 alt="${product.name}">
            <div class="flex-grow-1">
                <h6 class="mb-1">
                    <a href="/products/${product.id}/" class="text-decoration-none">
                        ${product.name}
                    </a>
                </h6>
                <div class="text-primary fw-bold">${utils.formatCurrency(product.price)}</div>
            </div>
        </div>
    `).join('');
}

function renderActivityList(activities) {
    const container = document.getElementById('activityList');
    
    if (!activities || activities.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-history"></i>
                <h5>No Activity Yet</h5>
                <p>Start browsing products to see your activity here</p>
                <a href="{% url 'products' %}" class="btn btn-primary">Browse Products</a>
            </div>
        `;
        return;
    }
    
    container.innerHTML = activities.map(activity => `
        <div class="activity-item">
            <div class="activity-icon ${activity.event_type}">
                <i class="fas fa-${getActivityIcon(activity.event_type)}"></i>
            </div>
            <div class="flex-grow-1">
                <div class="fw-medium">${getActivityDescription(activity)}</div>
                <small class="text-muted">${utils.formatDate(activity.timestamp)}</small>
                ${activity.product ? `
                    <div class="mt-1">
                        <a href="/products/${activity.product.id}/" class="text-decoration-none small">
                            ${activity.product.name}
                        </a>
                    </div>
                ` : ''}
            </div>
        </div>
    `).join('');
}

function renderReviewsList(reviews) {
    const container = document.getElementById('reviewsList');
    
    if (!reviews || reviews.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-star"></i>
                <h5>No Reviews Yet</h5>
                <p>Share your experience by writing product reviews</p>
                <a href="{% url 'products' %}" class="btn btn-primary">Find Products to Review</a>
            </div>
        `;
        return;
    }
    
    container.innerHTML = reviews.map(review => `
        <div class="review-card">
            <div class="d-flex justify-content-between align-items-start mb-2">
                <div>
                    <h6 class="mb-1">
                        <a href="/products/${review.product_id}/" class="text-decoration-none">
                            ${review.product_name}
                        </a>
                    </h6>
                    <div class="review-rating">
                        ${'★'.repeat(review.rating)}${'☆'.repeat(5-review.rating)}
                    </div>
                </div>
                <div class="text-end">
                    <small class="text-muted">${utils.formatDate(review.created_at)}</small>
                    ${review.sentiment_label ? `
                        <div class="mt-1">
                            <span class="sentiment-badge sentiment-${review.sentiment_label}">
                                ${review.sentiment_label}
                            </span>
                        </div>
                    ` : ''}
                </div>
            </div>
            
            ${review.title ? `<h6>${review.title}</h6>` : ''}
            ${review.comment ? `<p class="text-muted mb-0">${review.comment}</p>` : ''}
        </div>
    `).join('');
}

function renderRecommendationsList(recommendations) {
    const container = document.getElementById('recommendationsList');
    
    if (!recommendations || recommendations.length === 0) {
        container.innerHTML = `
            <div class="col-12">
                <div class="empty-state">
                    <i class="fas fa-lightbulb"></i>
                    <h5>No Recommendations Yet</h5>
                    <p>Browse and interact with products to get personalized recommendations</p>
                    <a href="{% url 'products' %}" class="btn btn-primary">Start Shopping</a>
                </div>
            </div>
        `;
        return;
    }
    
    container.innerHTML = recommendations.map(product => `
        <div class="col-lg-3 col-md-4 col-sm-6">
            <div class="recommendation-card">
                <div class="position-relative">
                    <img src="${product.image_url || '/static/images/product-placeholder.jpg'}" 
                         class="recommendation-image" 
                         alt="${product.name}">
                    <div class="recommendation-badge">
                        ${Math.round(product.recommendation_score * 100)}% Match
                    </div>
                </div>
                <div class="p-3">
                    <h6>
                        <a href="/products/${product.id}/" class="text-decoration-none">
                            ${product.name}
                        </a>
                    </h6>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-bold text-primary">${utils.formatCurrency(product.price)}</span>
                        <div class="rating-stars small">
                            ${'★'.repeat(Math.floor(product.rating))}${'☆'.repeat(5-Math.floor(product.rating))}
                        </div>
                    </div>
                    <small class="text-muted">${product.shop.name}</small>
                    
                    ${product.recommendation_reasons && product.recommendation_reasons.length > 0 ? `
                        <div class="mt-2">
                            <small class="text-success">
                                <i class="fas fa-check me-1"></i>
                                ${product.recommendation_reasons[0]}
                            </small>
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `).join('');
}

function setupNavigation() {
    const navLinks = document.querySelectorAll('.sidebar-nav .nav-link');
    const sections = document.querySelectorAll('.dashboard-section');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update active nav link
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            
            // Show corresponding section
            const sectionId = this.dataset.section + '-section';
            sections.forEach(section => {
                section.classList.add('d-none');
            });
            document.getElementById(sectionId).classList.remove('d-none');
            
            // Load section-specific data if needed
            loadSectionData(this.dataset.section);
        });
    });
}

function loadSectionData(section) {
    switch(section) {
        case 'wishlist':
            loadWishlistItems();
            break;
        // Add other section-specific data loading here
    }
}

function loadWishlistItems() {
    const container = document.getElementById('wishlistItems');
    
    // This would typically fetch from a wishlist API
    // For now, we'll show a placeholder
    container.innerHTML = `
        <div class="col-12">
            <div class="empty-state">
                <i class="fas fa-heart"></i>
                <h5>No Saved Items</h5>
                <p>Save products you're interested in to see them here</p>
                <a href="{% url 'products' %}" class="btn btn-primary">Browse Products</a>
            </div>
        </div>
    `;
}

function setupForms() {
    // Profile form
    document.getElementById('profileForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = {
            email: document.getElementById('email').value,
            first_name: document.getElementById('firstName').value,
            last_name: document.getElementById('lastName').value
        };
        
        // This would typically send to a profile update API
        utils.showAlert('Profile updated successfully', 'success');
    });
    
    // Preferences form
    document.getElementById('preferencesForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const preferences = {
            email_price_alerts: document.getElementById('emailPriceAlerts').checked,
            email_recommendations: document.getElementById('emailRecommendations').checked,
            email_newsletter: document.getElementById('emailNewsletter').checked,
            currency: document.getElementById('currency').value
        };
        
        // This would typically send to a preferences API
        utils.showAlert('Preferences saved successfully', 'success');
    });
}

function getActivityIcon(eventType) {
    const icons = {
        'product_view': 'eye',
        'product_like': 'heart',
        'save_product': 'bookmark',
        'review_submitted': 'star',
        'comparison_created': 'balance-scale',
        'search': 'search',
        'share_product': 'share'
    };
    return icons[eventType] || 'circle';
}

function getActivityDescription(activity) {
    const descriptions = {
        'product_view': 'Viewed a product',
        'product_like': 'Liked a product',
        'save_product': 'Saved a product',
        'review_submitted': 'Wrote a review',
        'comparison_created': 'Created a comparison',
        'search': `Searched for "${activity.event_data?.query || 'products'}"`,
        'share_product': 'Shared a product'
    };
    return descriptions[activity.event_type] || 'Performed an action';
}
</script>
{% endblock %}
