{% extends 'base.html' %}

{% block title %}Best in Click - Smart Shopping Platform{% endblock %}

{% block extra_head %}
<meta name="description" content="Best in Click - Your intelligent shopping companion. Compare products, discover deals, and get AI-powered recommendations from thousands of stores worldwide.">
<style>
  .hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 2rem;
    padding: 4rem 2rem;
    margin-bottom: 3rem;
    position: relative;
    overflow: hidden;
  }
  .hero-section::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="white" opacity="0.1"/></svg>') repeat;
    animation: float 20s infinite linear;
  }
  @keyframes float {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(-50px) translateY(-50px); }
  }
  .hero-content {
    position: relative;
    z-index: 2;
  }
  .hero-section h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  .hero-section p {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.95;
  }
  .search-box {
    background: white;
    border-radius: 3rem;
    padding: 0.5rem;
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    max-width: 600px;
  }
  .search-box input {
    border: none;
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
  }
  .search-box input:focus {
    outline: none;
    box-shadow: none;
  }
  .search-btn {
    border-radius: 2.5rem;
    padding: 1rem 2rem;
    font-weight: 600;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
  }
  .stats-section {
    background: white;
    border-radius: 2rem;
    padding: 2rem;
    margin: -2rem 0 3rem 0;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    position: relative;
    z-index: 3;
  }
  .stat-item {
    text-align: center;
    padding: 1rem;
  }
  .stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #667eea;
    display: block;
  }
  .stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.5rem;
  }
  .section-title {
    font-size: 2.2rem;
    font-weight: bold;
    color: #2d3748;
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
  }
  .section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 2px;
  }
  .category-card {
    background: white;
    border: none;
    border-radius: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    height: 100%;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
  }
  .category-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.15);
  }
  .category-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    color: white;
  }
  .product-card {
    background: white;
    border: none;
    border-radius: 1.5rem;
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
  }
  .product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
  }
  .product-image {
    height: 200px;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #6c757d;
  }
  .price-tag {
    font-size: 1.4rem;
    font-weight: bold;
    color: #667eea;
  }
  .store-card {
    background: white;
    border: none;
    border-radius: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    height: 100%;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
  }
  .store-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
  }
  .store-logo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
  }
  .review-card {
    background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
    border: none;
    border-radius: 1.5rem;
    height: 100%;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
  }
  .feature-card {
    background: white;
    border: none;
    border-radius: 1.5rem;
    transition: all 0.3s ease;
    height: 100%;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
  }
  .feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
  }
  .feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
  }
  .cta-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 2rem;
    padding: 3rem 2rem;
    margin: 3rem 0;
    text-align: center;
  }
  .app-download {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 1.5rem;
    padding: 2rem;
    height: 100%;
  }
  .store-owner {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    color: white;
    border-radius: 1.5rem;
    padding: 2rem;
    height: 100%;
  }
</style>
{% endblock %}

<!-- Hero Section -->
<div class="hero-section">
  <div class="hero-content">
    <div class="row align-items-center">
      <div class="col-lg-8">
        <h1 class="animate__animated animate__fadeInUp">Discover, Compare, Shop Smarter</h1>
        <p class="animate__animated animate__fadeInUp animate__delay-1s">
          Your intelligent shopping companion powered by AI. Compare millions of products,
          discover exclusive deals, and get personalized recommendations from thousands of stores worldwide.
        </p>

        <!-- Search Box -->
        <div class="search-box animate__animated animate__fadeInUp animate__delay-2s">
          <form method="get" action="/search/" class="d-flex">
            <input type="text" name="q" class="form-control"
                   placeholder="Search for products, brands, or stores..."
                   id="heroSearch">
            <button class="btn search-btn" type="submit">
              <i class="fas fa-search me-2"></i>Search
            </button>
          </form>
        </div>

        <!-- Quick Actions -->
        <div class="mt-4 animate__animated animate__fadeInUp animate__delay-3s">
          <a href="/recommendations/" class="btn btn-outline-light btn-lg me-3">
            <i class="fas fa-robot me-2"></i>AI Recommendations
          </a>
          <a href="/compare/" class="btn btn-outline-light btn-lg">
            <i class="fas fa-balance-scale me-2"></i>Compare Products
          </a>
        </div>
      </div>

      <div class="col-lg-4 d-none d-lg-block text-center">
        <div class="animate__animated animate__fadeInRight animate__delay-1s">
          <i class="fas fa-shopping-cart" style="font-size: 10rem; opacity: 0.2;"></i>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Statistics Section -->
<div class="container">
  <div class="stats-section">
    <div class="row">
      <div class="col-md-3">
        <div class="stat-item">
          <span class="stat-number" data-count="50000">0</span>
          <div class="stat-label">Products</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stat-item">
          <span class="stat-number" data-count="1200">0</span>
          <div class="stat-label">Stores</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stat-item">
          <span class="stat-number" data-count="25000">0</span>
          <div class="stat-label">Happy Customers</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stat-item">
          <span class="stat-number" data-count="98">0</span>
          <div class="stat-label">Satisfaction Rate</div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Featured Categories -->
<div class="container">
  <h2 class="section-title">Featured Categories</h2>
  <div class="row mb-5">
    <div class="col-lg-2 col-md-4 col-6 mb-4">
      <div class="category-card text-center p-4" onclick="navigateToCategory('electronics')">
        <div class="category-icon" style="background: linear-gradient(45deg, #667eea, #764ba2);">
          <i class="fas fa-laptop"></i>
        </div>
        <h6 class="fw-bold mb-2">Electronics</h6>
        <p class="text-muted small mb-0">Latest gadgets & tech</p>
        <small class="text-primary">12,450+ products</small>
      </div>
    </div>

    <div class="col-lg-2 col-md-4 col-6 mb-4">
      <div class="category-card text-center p-4" onclick="navigateToCategory('fashion')">
        <div class="category-icon" style="background: linear-gradient(45deg, #28a745, #20c997);">
          <i class="fas fa-tshirt"></i>
        </div>
        <h6 class="fw-bold mb-2">Fashion</h6>
        <p class="text-muted small mb-0">Trendy clothing & accessories</p>
        <small class="text-primary">8,320+ products</small>
      </div>
    </div>

    <div class="col-lg-2 col-md-4 col-6 mb-4">
      <div class="category-card text-center p-4" onclick="navigateToCategory('home')">
        <div class="category-icon" style="background: linear-gradient(45deg, #ffc107, #fd7e14);">
          <i class="fas fa-home"></i>
        </div>
        <h6 class="fw-bold mb-2">Home & Garden</h6>
        <p class="text-muted small mb-0">Furniture & decor</p>
        <small class="text-primary">6,780+ products</small>
      </div>
    </div>

    <div class="col-lg-2 col-md-4 col-6 mb-4">
      <div class="category-card text-center p-4" onclick="navigateToCategory('sports')">
        <div class="category-icon" style="background: linear-gradient(45deg, #dc3545, #e83e8c);">
          <i class="fas fa-dumbbell"></i>
        </div>
        <h6 class="fw-bold mb-2">Sports</h6>
        <p class="text-muted small mb-0">Fitness & outdoor gear</p>
        <small class="text-primary">4,560+ products</small>
      </div>
    </div>

    <div class="col-lg-2 col-md-4 col-6 mb-4">
      <div class="category-card text-center p-4" onclick="navigateToCategory('books')">
        <div class="category-icon" style="background: linear-gradient(45deg, #17a2b8, #6f42c1);">
          <i class="fas fa-book"></i>
        </div>
        <h6 class="fw-bold mb-2">Books</h6>
        <p class="text-muted small mb-0">Educational & entertainment</p>
        <small class="text-primary">3,240+ products</small>
      </div>
    </div>

    <div class="col-lg-2 col-md-4 col-6 mb-4">
      <div class="category-card text-center p-4" onclick="navigateToCategory('gaming')">
        <div class="category-icon" style="background: linear-gradient(45deg, #6c757d, #495057);">
          <i class="fas fa-gamepad"></i>
        </div>
        <h6 class="fw-bold mb-2">Gaming</h6>
        <p class="text-muted small mb-0">Games & consoles</p>
        <small class="text-primary">2,890+ products</small>
      </div>
    </div>
  </div>
</div>

<!-- AI Recommendations -->
<div class="container">
  <h2 class="section-title">AI-Powered Recommendations</h2>
  <div class="text-center mb-4">
    <p class="text-muted">Personalized product suggestions based on your preferences and shopping behavior</p>
  </div>

  <div class="row mb-5">
    <div class="col-lg-3 col-md-6 mb-4">
      <div class="product-card">
        <div class="product-image">
          <i class="fas fa-mobile-alt"></i>
        </div>
        <div class="card-body p-4">
          <div class="d-flex justify-content-between align-items-start mb-2">
            <span class="badge bg-primary">Electronics</span>
            <button class="btn btn-outline-danger btn-sm" onclick="toggleWishlist(1)">
              <i class="far fa-heart"></i>
            </button>
          </div>
          <h6 class="card-title fw-bold">iPhone 15 Pro Max</h6>
          <p class="card-text text-muted small mb-3">Latest flagship with titanium design and advanced camera system</p>

          <div class="d-flex align-items-center mb-3">
            <div class="text-warning me-2">
              ★★★★★
            </div>
            <small class="text-muted">(4.8) 2,341 reviews</small>
          </div>

          <div class="d-flex justify-content-between align-items-center">
            <div>
              <span class="price-tag">$1,199</span>
              <small class="text-decoration-line-through text-muted ms-2">$1,299</small>
            </div>
            <span class="badge bg-success">-8%</span>
          </div>
        </div>
        <div class="card-footer bg-transparent p-4 pt-0">
          <div class="d-flex gap-2">
            <button class="btn btn-primary flex-fill" onclick="viewProduct(1)">
              <i class="fas fa-eye me-1"></i>View
            </button>
            <button class="btn btn-outline-primary" onclick="addToCompare(1)">
              <i class="fas fa-balance-scale"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
      <div class="product-card">
        <div class="product-image">
          <i class="fas fa-headphones"></i>
        </div>
        <div class="card-body p-4">
          <div class="d-flex justify-content-between align-items-start mb-2">
            <span class="badge bg-primary">Electronics</span>
            <button class="btn btn-outline-danger btn-sm" onclick="toggleWishlist(2)">
              <i class="far fa-heart"></i>
            </button>
          </div>
          <h6 class="card-title fw-bold">Sony WH-1000XM5</h6>
          <p class="card-text text-muted small mb-3">Premium noise-canceling wireless headphones with 30-hour battery</p>

          <div class="d-flex align-items-center mb-3">
            <div class="text-warning me-2">
              ★★★★★
            </div>
            <small class="text-muted">(4.7) 1,856 reviews</small>
          </div>

          <div class="d-flex justify-content-between align-items-center">
            <div>
              <span class="price-tag">$349</span>
              <small class="text-decoration-line-through text-muted ms-2">$399</small>
            </div>
            <span class="badge bg-success">-13%</span>
          </div>
        </div>
        <div class="card-footer bg-transparent p-4 pt-0">
          <div class="d-flex gap-2">
            <button class="btn btn-primary flex-fill" onclick="viewProduct(2)">
              <i class="fas fa-eye me-1"></i>View
            </button>
            <button class="btn btn-outline-primary" onclick="addToCompare(2)">
              <i class="fas fa-balance-scale"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
      <div class="product-card">
        <div class="product-image">
          <i class="fas fa-watch"></i>
        </div>
        <div class="card-body p-4">
          <div class="d-flex justify-content-between align-items-start mb-2">
            <span class="badge bg-primary">Electronics</span>
            <button class="btn btn-outline-danger btn-sm" onclick="toggleWishlist(3)">
              <i class="far fa-heart"></i>
            </button>
          </div>
          <h6 class="card-title fw-bold">Apple Watch Series 9</h6>
          <p class="card-text text-muted small mb-3">Advanced health monitoring and fitness tracking smartwatch</p>

          <div class="d-flex align-items-center mb-3">
            <div class="text-warning me-2">
              ★★★★☆
            </div>
            <small class="text-muted">(4.6) 3,124 reviews</small>
          </div>

          <div class="d-flex justify-content-between align-items-center">
            <div>
              <span class="price-tag">$399</span>
            </div>
            <span class="badge bg-info">New</span>
          </div>
        </div>
        <div class="card-footer bg-transparent p-4 pt-0">
          <div class="d-flex gap-2">
            <button class="btn btn-primary flex-fill" onclick="viewProduct(3)">
              <i class="fas fa-eye me-1"></i>View
            </button>
            <button class="btn btn-outline-primary" onclick="addToCompare(3)">
              <i class="fas fa-balance-scale"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
      <div class="product-card">
        <div class="product-image">
          <i class="fas fa-camera"></i>
        </div>
        <div class="card-body p-4">
          <div class="d-flex justify-content-between align-items-start mb-2">
            <span class="badge bg-primary">Electronics</span>
            <button class="btn btn-outline-danger btn-sm" onclick="toggleWishlist(4)">
              <i class="far fa-heart"></i>
            </button>
          </div>
          <h6 class="card-title fw-bold">Canon EOS R6 Mark II</h6>
          <p class="card-text text-muted small mb-3">Professional mirrorless camera with 24.2MP full-frame sensor</p>

          <div class="d-flex align-items-center mb-3">
            <div class="text-warning me-2">
              ★★★★★
            </div>
            <small class="text-muted">(4.9) 892 reviews</small>
          </div>

          <div class="d-flex justify-content-between align-items-center">
            <div>
              <span class="price-tag">$2,499</span>
              <small class="text-decoration-line-through text-muted ms-2">$2,699</small>
            </div>
            <span class="badge bg-success">-7%</span>
          </div>
        </div>
        <div class="card-footer bg-transparent p-4 pt-0">
          <div class="d-flex gap-2">
            <button class="btn btn-primary flex-fill" onclick="viewProduct(4)">
              <i class="fas fa-eye me-1"></i>View
            </button>
            <button class="btn btn-outline-primary" onclick="addToCompare(4)">
              <i class="fas fa-balance-scale"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="text-center">
    <a href="/recommendations/" class="btn btn-outline-primary btn-lg">
      <i class="fas fa-robot me-2"></i>View All AI Recommendations
    </a>
  </div>
</div>

<!-- Smart Features -->
<div class="container">
  <h2 class="section-title">Smart Shopping Features</h2>
  <div class="row mb-5">
    <div class="col-lg-4 col-md-6 mb-4">
      <div class="feature-card text-center p-4">
        <div class="feature-icon" style="background: linear-gradient(45deg, #667eea, #764ba2);">
          <i class="fas fa-robot"></i>
        </div>
        <h5 class="fw-bold mb-3">AI Recommendations</h5>
        <p class="text-muted">Get personalized product suggestions powered by advanced machine learning algorithms that understand your preferences.</p>
        <a href="/recommendations/" class="btn btn-outline-primary">Try Now</a>
      </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-4">
      <div class="feature-card text-center p-4">
        <div class="feature-icon" style="background: linear-gradient(45deg, #28a745, #20c997);">
          <i class="fas fa-balance-scale"></i>
        </div>
        <h5 class="fw-bold mb-3">Smart Comparison</h5>
        <p class="text-muted">Compare products side-by-side with detailed specifications, prices, and reviews from multiple stores instantly.</p>
        <a href="/compare/" class="btn btn-outline-primary">Compare Now</a>
      </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-4">
      <div class="feature-card text-center p-4">
        <div class="feature-icon" style="background: linear-gradient(45deg, #ffc107, #fd7e14);">
          <i class="fas fa-bell"></i>
        </div>
        <h5 class="fw-bold mb-3">Price Alerts</h5>
        <p class="text-muted">Never miss a deal! Get instant notifications when prices drop on products in your wishlist.</p>
        <a href="/wishlist/" class="btn btn-outline-primary">Set Alerts</a>
      </div>
    </div>
  </div>
</div>

<!-- Trusted Store Partners -->
<div class="container">
  <h2 class="section-title">Trusted Store Partners</h2>
  <div class="row mb-5">
    <div class="col-lg-3 col-md-6 mb-4">
      <div class="store-card text-center p-4" onclick="visitStore('amazon')">
        <div class="store-logo" style="background: linear-gradient(45deg, #ff9900, #ffad33);">
          <i class="fab fa-amazon"></i>
        </div>
        <h6 class="fw-bold mb-2">Amazon</h6>
        <p class="text-muted small mb-2">Everything you need, delivered fast</p>
        <div class="d-flex justify-content-center align-items-center mb-2">
          <div class="text-warning me-2">★★★★★</div>
          <small class="text-muted">(4.5)</small>
        </div>
        <small class="text-primary">15,000+ products</small>
      </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
      <div class="store-card text-center p-4" onclick="visitStore('ebay')">
        <div class="store-logo" style="background: linear-gradient(45deg, #0064d2, #0053ba);">
          <i class="fab fa-ebay"></i>
        </div>
        <h6 class="fw-bold mb-2">eBay</h6>
        <p class="text-muted small mb-2">Buy & sell with confidence</p>
        <div class="d-flex justify-content-center align-items-center mb-2">
          <div class="text-warning me-2">★★★★☆</div>
          <small class="text-muted">(4.3)</small>
        </div>
        <small class="text-primary">8,500+ products</small>
      </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
      <div class="store-card text-center p-4" onclick="visitStore('bestbuy')">
        <div class="store-logo" style="background: linear-gradient(45deg, #0046be, #003d99);">
          <i class="fas fa-store"></i>
        </div>
        <h6 class="fw-bold mb-2">Best Buy</h6>
        <p class="text-muted small mb-2">Electronics & tech specialist</p>
        <div class="d-flex justify-content-center align-items-center mb-2">
          <div class="text-warning me-2">★★★★☆</div>
          <small class="text-muted">(4.2)</small>
        </div>
        <small class="text-primary">6,200+ products</small>
      </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
      <div class="store-card text-center p-4" onclick="visitStore('walmart')">
        <div class="store-logo" style="background: linear-gradient(45deg, #004c91, #0071ce);">
          <i class="fas fa-shopping-cart"></i>
        </div>
        <h6 class="fw-bold mb-2">Walmart</h6>
        <p class="text-muted small mb-2">Save money, live better</p>
        <div class="d-flex justify-content-center align-items-center mb-2">
          <div class="text-warning me-2">★★★★☆</div>
          <small class="text-muted">(4.1)</small>
        </div>
        <small class="text-primary">12,300+ products</small>
      </div>
    </div>
  </div>

  <div class="text-center">
    <a href="/stores/" class="btn btn-outline-primary btn-lg">
      <i class="fas fa-store me-2"></i>View All Stores
    </a>
  </div>
</div>

<!-- Customer Reviews -->
<h2 class="section-title">💬 Customer Reviews</h2>
<div class="row mb-5">
  <div class="col-md-4 mb-3">
    <div class="card review-card h-100 shadow-sm">
      <div class="card-body">
        <h6 class="card-title">John D. <span class="badge bg-warning text-dark">5 <i class="fas fa-star"></i></span></h6>
        <p class="card-text">"Amazing platform! Found the best deals and the AI recommendations are spot on. Highly recommended!"</p>
        <small class="text-muted">On Latest Smartphone</small>
      </div>
    </div>
  </div>
  <div class="col-md-4 mb-3">
    <div class="card review-card h-100 shadow-sm">
      <div class="card-body">
        <h6 class="card-title">Sarah M. <span class="badge bg-warning text-dark">5 <i class="fas fa-star"></i></span></h6>
        <p class="card-text">"Love the comparison feature! Saved me hours of research and helped me make the right choice."</p>
        <small class="text-muted">On Wireless Headphones</small>
      </div>
    </div>
  </div>
  <div class="col-md-4 mb-3">
    <div class="card review-card h-100 shadow-sm">
      <div class="card-body">
        <h6 class="card-title">Mike R. <span class="badge bg-warning text-dark">4 <i class="fas fa-star"></i></span></h6>
        <p class="card-text">"Great user experience and excellent customer service. The AI suggestions are very helpful!"</p>
        <small class="text-muted">On Smart Watch</small>
      </div>
    </div>
  </div>
</div>

<!-- App Download & CTA -->
<div class="row mb-5">
  <div class="col-md-6 mb-3">
    <div class="p-4 bg-light rounded shadow-sm h-100">
      <h5 class="mb-2"><i class="fab fa-android"></i> <i class="fab fa-apple"></i> Download Our App</h5>
      <p>Shop smarter and faster with our mobile app. Get exclusive deals and personalized recommendations!</p>
      <a href="#" class="btn btn-success me-2"><i class="fab fa-android"></i> Google Play</a>
      <a href="#" class="btn btn-dark"><i class="fab fa-apple"></i> App Store</a>
    </div>
  </div>
  <div class="col-md-6 mb-3">
    <div class="p-4 bg-primary text-white rounded shadow-sm h-100">
      <h5 class="mb-2">Are you a store owner?</h5>
      <p>Join Best in Click to reach more customers and grow your business with our AI-powered platform.</p>
      <a href="/register/" class="btn btn-light">Register Your Store</a>
    </div>
  </div>
</div>

<!-- Features Section -->
<div class="row mb-5">
  <div class="col-md-4 mb-3">
    <div class="card h-100 text-center shadow-sm">
      <div class="card-body">
        <i class="fas fa-search fa-3x text-primary mb-3"></i>
        <h5 class="card-title">Smart Search</h5>
        <p class="card-text">Find exactly what you're looking for with our intelligent search system powered by AI.</p>
      </div>
    </div>
  </div>
  <div class="col-md-4 mb-3">
    <div class="card h-100 text-center shadow-sm">
      <div class="card-body">
        <i class="fas fa-balance-scale fa-3x text-success mb-3"></i>
        <h5 class="card-title">Compare Products</h5>
        <p class="card-text">Compare features, prices, and reviews side by side to make the best purchasing decision.</p>
      </div>
    </div>
  </div>
  <div class="col-md-4 mb-3">
    <div class="card h-100 text-center shadow-sm">
      <div class="card-body">
        <i class="fas fa-robot fa-3x text-warning mb-3"></i>
        <h5 class="card-title">AI Recommendations</h5>
        <p class="card-text">Get personalized product recommendations based on your preferences and shopping history.</p>
      </div>
    </div>
  </div>
</div>

<!-- Call to Action -->
<div class="cta-section">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-8">
        <h2 class="fw-bold mb-3">Ready to Start Smart Shopping?</h2>
        <p class="lead mb-4">Join thousands of satisfied customers who save time and money with Best in Click. Get personalized recommendations, exclusive deals, and smart price alerts.</p>
        <div class="d-flex flex-wrap gap-3">
          <a href="/register/" class="btn btn-light btn-lg">
            <i class="fas fa-user-plus me-2"></i>Sign Up Free
          </a>
          <a href="/products/" class="btn btn-outline-light btn-lg">
            <i class="fas fa-search me-2"></i>Start Shopping
          </a>
          <a href="/recommendations/" class="btn btn-outline-light btn-lg">
            <i class="fas fa-robot me-2"></i>Try AI Recommendations
          </a>
        </div>
      </div>
      <div class="col-lg-4 text-center">
        <div class="row">
          <div class="col-6">
            <div class="app-download text-center p-3 mb-3">
              <i class="fab fa-apple fa-2x mb-2"></i>
              <h6>Download for iOS</h6>
              <small>Available on App Store</small>
            </div>
          </div>
          <div class="col-6">
            <div class="store-owner text-center p-3 mb-3">
              <i class="fas fa-store fa-2x mb-2"></i>
              <h6>Become a Partner</h6>
              <small>List your store</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

</div>

<script>
// Counter animation for statistics
function animateCounters() {
  const counters = document.querySelectorAll('.stat-number');

  counters.forEach(counter => {
    const target = parseInt(counter.getAttribute('data-count'));
    const increment = target / 100;
    let current = 0;

    const timer = setInterval(() => {
      current += increment;
      if (current >= target) {
        counter.textContent = target.toLocaleString();
        clearInterval(timer);
      } else {
        counter.textContent = Math.floor(current).toLocaleString();
      }
    }, 20);
  });
}

// Search functionality
document.addEventListener('DOMContentLoaded', function() {
  const heroSearch = document.getElementById('heroSearch');
  if (heroSearch) {
    heroSearch.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        const query = this.value.trim();
        if (query) {
          window.location.href = `/search/?q=${encodeURIComponent(query)}`;
        }
      }
    });
  }
});

// Category navigation
function navigateToCategory(category) {
  window.location.href = `/products/?category=${category}`;
}

// Product interactions
function viewProduct(productId) {
  window.location.href = `/products/${productId}/`;
}

function toggleWishlist(productId) {
  const btn = event.target.closest('button');
  const icon = btn.querySelector('i');

  if (icon.classList.contains('far')) {
    icon.classList.remove('far');
    icon.classList.add('fas');
    btn.classList.remove('btn-outline-danger');
    btn.classList.add('btn-danger');
    showToast('Added to wishlist!', 'success');
  } else {
    icon.classList.remove('fas');
    icon.classList.add('far');
    btn.classList.remove('btn-danger');
    btn.classList.add('btn-outline-danger');
    showToast('Removed from wishlist', 'info');
  }
}

function addToCompare(productId) {
  showToast('Added to comparison!', 'info');
  // Update compare counter in navigation if exists
  const compareCounter = document.querySelector('.compare-counter');
  if (compareCounter) {
    const current = parseInt(compareCounter.textContent) || 0;
    compareCounter.textContent = current + 1;
  }
}

// Store interactions
function visitStore(storeId) {
  window.location.href = `/stores/${storeId}/`;
}

// Toast notifications
function showToast(message, type = 'info') {
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} position-fixed`;
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px; animation: slideInRight 0.3s ease;';
  toast.innerHTML = `
    ${message}
    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
  `;
  document.body.appendChild(toast);

  setTimeout(() => {
    if (toast.parentElement) {
      toast.style.animation = 'slideOutRight 0.3s ease';
      setTimeout(() => toast.remove(), 300);
    }
  }, 3000);
}

// Initialize animations when page loads
document.addEventListener('DOMContentLoaded', function() {
  // Animate counters when they come into view
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        animateCounters();
        observer.unobserve(entry.target);
      }
    });
  });

  const statsSection = document.querySelector('.stats-section');
  if (statsSection) {
    observer.observe(statsSection);
  }

  // Add hover effects to cards
  document.querySelectorAll('.category-card, .product-card, .store-card, .feature-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-5px)';
    });

    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
    });
  });
});
</script>

<style>
@keyframes slideInRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutRight {
  from { transform: translateX(0); opacity: 1; }
  to { transform: translateX(100%); opacity: 0; }
}

.animate__animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

.animate__fadeInUp {
  animation-name: fadeInUp;
}

.animate__fadeInRight {
  animation-name: fadeInRight;
}

.animate__delay-1s { animation-delay: 0.5s; }
.animate__delay-2s { animation-delay: 1s; }
.animate__delay-3s { animation-delay: 1.5s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 40px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translate3d(40px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
</style>
{% endblock %}
