{% extends 'base.html' %}
{% load static %}

{% block title %}Login - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="Login to your Best in Click account to access personalized recommendations, wishlist, and exclusive deals.">
<style>
  .login-container {
    min-height: 80vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  }
  .login-card {
    background: white;
    border-radius: 1.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
    max-width: 900px;
    margin: 0 auto;
  }
  .login-left {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    color: white;
    padding: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
  }
  .login-right {
    padding: 3rem;
  }
  .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }
  .social-login {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 0.75rem;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
  }
  .social-login:hover {
    background: #f8f9fa;
    text-decoration: none;
  }
  .social-login.google {
    color: #db4437;
  }
  .social-login.facebook {
    color: #4267B2;
  }
  .social-login.apple {
    color: #000;
  }
  .divider {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
  }
  .divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #dee2e6;
  }
  .divider span {
    background: white;
    padding: 0 1rem;
    color: #6c757d;
  }
  .password-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    border: none;
    background: none;
    color: #6c757d;
    cursor: pointer;
  }
  .login-benefits {
    background: #e3f0ff;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-top: 2rem;
  }
  .benefit-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
  }
  .benefit-item:last-child {
    margin-bottom: 0;
  }
  .benefit-icon {
    width: 40px;
    height: 40px;
    background: #0d6efd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
    flex-shrink: 0;
  }
  .remember-forgot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1rem 0;
  }
  .form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
  }
  .alert {
    border-radius: 0.5rem;
    margin-bottom: 1rem;
  }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
  <div class="container">
    <div class="login-card">
      <div class="row g-0">
        <!-- Left Side - Welcome -->
        <div class="col-lg-5 d-none d-lg-block">
          <div class="login-left">
            <div>
              <i class="fas fa-shopping-cart fa-4x mb-4" style="opacity: 0.8;"></i>
              <h2 class="fw-bold mb-3">Welcome Back!</h2>
              <p class="lead mb-4">Sign in to access your personalized shopping experience with AI-powered recommendations.</p>
              
              <div class="login-benefits">
                <div class="benefit-item">
                  <div class="benefit-icon">
                    <i class="fas fa-heart"></i>
                  </div>
                  <div>
                    <h6 class="mb-1 text-dark">Wishlist & Favorites</h6>
                    <small class="text-muted">Save products you love</small>
                  </div>
                </div>
                
                <div class="benefit-item">
                  <div class="benefit-icon">
                    <i class="fas fa-robot"></i>
                  </div>
                  <div>
                    <h6 class="mb-1 text-dark">AI Recommendations</h6>
                    <small class="text-muted">Personalized product suggestions</small>
                  </div>
                </div>
                
                <div class="benefit-item">
                  <div class="benefit-icon">
                    <i class="fas fa-bell"></i>
                  </div>
                  <div>
                    <h6 class="mb-1 text-dark">Price Alerts</h6>
                    <small class="text-muted">Get notified of deals</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Right Side - Login Form -->
        <div class="col-lg-7">
          <div class="login-right">
            <div class="text-center mb-4">
              <h3 class="fw-bold">Sign In</h3>
              <p class="text-muted">Enter your credentials to access your account</p>
            </div>

            <!-- Error Messages -->
            {% if messages %}
              {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                  {{ message }}
                  <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
              {% endfor %}
            {% endif %}

            <!-- Social Login -->
            <div class="mb-4">
              <a href="#" class="social-login google" onclick="socialLogin('google')">
                <i class="fab fa-google me-2"></i>
                Continue with Google
              </a>
              
              <a href="#" class="social-login facebook" onclick="socialLogin('facebook')">
                <i class="fab fa-facebook-f me-2"></i>
                Continue with Facebook
              </a>
              
              <a href="#" class="social-login apple" onclick="socialLogin('apple')">
                <i class="fab fa-apple me-2"></i>
                Continue with Apple
              </a>
            </div>

            <div class="divider">
              <span>or sign in with email</span>
            </div>

            <!-- Login Form -->
            <form method="post" id="loginForm">
              {% csrf_token %}
              
              <div class="mb-3">
                <label for="email" class="form-label">Email Address</label>
                <input type="email" class="form-control form-control-lg" id="email" name="email" 
                       placeholder="Enter your email" required>
                <div class="invalid-feedback">
                  Please enter a valid email address.
                </div>
              </div>
              
              <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <div class="position-relative">
                  <input type="password" class="form-control form-control-lg" id="password" name="password" 
                         placeholder="Enter your password" required>
                  <button type="button" class="password-toggle" onclick="togglePassword()">
                    <i class="fas fa-eye" id="passwordIcon"></i>
                  </button>
                </div>
                <div class="invalid-feedback">
                  Please enter your password.
                </div>
              </div>
              
              <div class="remember-forgot">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="remember" name="remember">
                  <label class="form-check-label" for="remember">
                    Remember me
                  </label>
                </div>
                <a href="/forgot-password/" class="text-decoration-none">Forgot password?</a>
              </div>
              
              <button type="submit" class="btn btn-primary btn-lg w-100 mb-3">
                <i class="fas fa-sign-in-alt me-2"></i>Sign In
              </button>
            </form>

            <!-- Sign Up Link -->
            <div class="text-center">
              <p class="text-muted">
                Don't have an account? 
                <a href="/register/" class="text-decoration-none fw-semibold">Create one now</a>
              </p>
            </div>

            <!-- Additional Links -->
            <div class="text-center mt-4">
              <div class="d-flex justify-content-center gap-3">
                <a href="/help/" class="text-muted text-decoration-none small">
                  <i class="fas fa-question-circle me-1"></i>Help
                </a>
                <a href="/privacy/" class="text-muted text-decoration-none small">
                  <i class="fas fa-shield-alt me-1"></i>Privacy
                </a>
                <a href="/terms/" class="text-muted text-decoration-none small">
                  <i class="fas fa-file-contract me-1"></i>Terms
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function togglePassword() {
  const passwordInput = document.getElementById('password');
  const passwordIcon = document.getElementById('passwordIcon');
  
  if (passwordInput.type === 'password') {
    passwordInput.type = 'text';
    passwordIcon.classList.remove('fa-eye');
    passwordIcon.classList.add('fa-eye-slash');
  } else {
    passwordInput.type = 'password';
    passwordIcon.classList.remove('fa-eye-slash');
    passwordIcon.classList.add('fa-eye');
  }
}

function socialLogin(provider) {
  console.log('Social login with:', provider);
  
  // Show loading state
  showToast(`Redirecting to ${provider}...`, 'info');
  
  // In a real implementation, this would redirect to the OAuth provider
  setTimeout(() => {
    showToast(`${provider} login would be implemented here`, 'warning');
  }, 1000);
}

// Form validation
document.getElementById('loginForm').addEventListener('submit', function(e) {
  const email = document.getElementById('email');
  const password = document.getElementById('password');
  let isValid = true;
  
  // Reset previous validation states
  document.querySelectorAll('.form-control').forEach(input => {
    input.classList.remove('is-invalid');
  });
  
  // Email validation
  if (!email.value || !isValidEmail(email.value)) {
    email.classList.add('is-invalid');
    isValid = false;
  }
  
  // Password validation
  if (!password.value || password.value.length < 6) {
    password.classList.add('is-invalid');
    password.nextElementSibling.nextElementSibling.textContent = 'Password must be at least 6 characters long.';
    isValid = false;
  }
  
  if (!isValid) {
    e.preventDefault();
    showToast('Please fix the errors in the form', 'danger');
  } else {
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Signing In...';
    submitBtn.disabled = true;
    
    // Re-enable button after 3 seconds (in case of error)
    setTimeout(() => {
      submitBtn.innerHTML = originalText;
      submitBtn.disabled = false;
    }, 3000);
  }
});

function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function showToast(message, type) {
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} position-fixed`;
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
  toast.innerHTML = `
    ${message}
    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
  `;
  document.body.appendChild(toast);
  
  setTimeout(() => {
    if (toast.parentElement) {
      toast.remove();
    }
  }, 3000);
}

// Auto-focus on email field
document.addEventListener('DOMContentLoaded', function() {
  document.getElementById('email').focus();
});

// Handle Enter key in password field
document.getElementById('password').addEventListener('keypress', function(e) {
  if (e.key === 'Enter') {
    document.getElementById('loginForm').dispatchEvent(new Event('submit'));
  }
});

// Demo credentials helper
document.addEventListener('DOMContentLoaded', function() {
  // Add demo credentials button for testing
  const demoBtn = document.createElement('button');
  demoBtn.type = 'button';
  demoBtn.className = 'btn btn-outline-secondary btn-sm w-100 mt-2';
  demoBtn.innerHTML = '<i class="fas fa-user me-1"></i>Use Demo Credentials';
  demoBtn.onclick = function() {
    document.getElementById('email').value = '<EMAIL>';
    document.getElementById('password').value = 'demo123';
    showToast('Demo credentials filled!', 'info');
  };
  
  // Insert after the sign in button
  const signInBtn = document.querySelector('button[type="submit"]');
  signInBtn.parentNode.insertBefore(demoBtn, signInBtn.nextSibling);
});
</script>
{% endblock %}
