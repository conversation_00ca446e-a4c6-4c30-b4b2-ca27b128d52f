{% extends 'base.html' %}

{% block title %}Notifications - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="View and manage your notifications on Best in Click. Stay updated with price alerts, deals, and account activities.">
<style>
  .notifications-header {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
  }
  .notifications-header::before {
    content: '🔔';
    position: absolute;
    top: -20px;
    right: -20px;
    font-size: 8rem;
    opacity: 0.1;
    transform: rotate(15deg);
  }
  .notification-filters {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  .filter-chip {
    background: #e9ecef;
    color: #495057;
    border: 1px solid #ced4da;
    border-radius: 2rem;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    display: inline-block;
  }
  .filter-chip.active {
    background: #17a2b8;
    color: white;
    border-color: #17a2b8;
  }
  .filter-chip:hover {
    background: #17a2b8;
    color: white;
    border-color: #17a2b8;
  }
  .notification-item {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }
  .notification-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.15);
  }
  .notification-item.unread {
    border-left: 4px solid #17a2b8;
  }
  .notification-item.unread::before {
    content: '';
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 8px;
    height: 8px;
    background: #17a2b8;
    border-radius: 50%;
  }
  .notification-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    margin-right: 1rem;
    flex-shrink: 0;
  }
  .notification-icon.price-alert { background: #28a745; }
  .notification-icon.deal { background: #dc3545; }
  .notification-icon.order { background: #0d6efd; }
  .notification-icon.review { background: #ffc107; color: #000; }
  .notification-icon.system { background: #6c757d; }
  .notification-icon.wishlist { background: #e83e8c; }
  .notification-actions {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  .notification-item:hover .notification-actions {
    opacity: 1;
  }
  .action-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  .action-btn.mark-read {
    background: #28a745;
  }
  .action-btn.delete {
    background: #dc3545;
  }
  .action-btn:hover {
    transform: scale(1.1);
  }
  .empty-notifications {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }
  .notification-settings {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  .setting-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
  }
  .setting-item:last-child {
    border-bottom: none;
  }
  .notification-time {
    color: #6c757d;
    font-size: 0.8rem;
  }
  .notification-content {
    flex-grow: 1;
  }
  .notification-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
  }
  .notification-text {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }
  .bulk-actions {
    background: white;
    border-radius: 1rem;
    padding: 1rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }
  .notification-stats {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
  }
  .stat-item {
    text-align: center;
    padding: 1rem;
  }
  .stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #17a2b8;
    display: block;
  }
  .stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.5rem;
  }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
  <!-- Notifications Header -->
  <div class="notifications-header">
    <div class="row align-items-center">
      <div class="col-md-8">
        <h1 class="fw-bold mb-2">
          <i class="fas fa-bell me-3"></i>Notifications
        </h1>
        <p class="mb-0">Stay updated with price alerts, deals, and account activities</p>
      </div>
      <div class="col-md-4 text-end">
        <button class="btn btn-light me-2" onclick="markAllAsRead()">
          <i class="fas fa-check-double me-2"></i>Mark All Read
        </button>
        <button class="btn btn-outline-light" onclick="toggleSettings()">
          <i class="fas fa-cog me-2"></i>Settings
        </button>
      </div>
    </div>
  </div>

  <!-- Notification Statistics -->
  <div class="notification-stats">
    <div class="row">
      <div class="col-md-3">
        <div class="stat-item">
          <span class="stat-number">{{ total_notifications|default:0 }}</span>
          <div class="stat-label">Total Notifications</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stat-item">
          <span class="stat-number">{{ unread_count|default:0 }}</span>
          <div class="stat-label">Unread</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stat-item">
          <span class="stat-number">{{ price_alerts_count|default:0 }}</span>
          <div class="stat-label">Price Alerts</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stat-item">
          <span class="stat-number">{{ deals_count|default:0 }}</span>
          <div class="stat-label">Deal Notifications</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Notification Filters -->
  <div class="notification-filters">
    <div class="d-flex justify-content-between align-items-center mb-3">
      <h5 class="fw-bold mb-0">Filter Notifications</h5>
      <div>
        <button class="btn btn-outline-primary btn-sm" onclick="selectAll()">Select All</button>
        <button class="btn btn-outline-danger btn-sm ms-2" onclick="deleteSelected()">Delete Selected</button>
      </div>
    </div>
    
    <div class="filter-chips">
      <span class="filter-chip active" onclick="filterNotifications('all')">All</span>
      <span class="filter-chip" onclick="filterNotifications('unread')">Unread</span>
      <span class="filter-chip" onclick="filterNotifications('price-alert')">Price Alerts</span>
      <span class="filter-chip" onclick="filterNotifications('deal')">Deals</span>
      <span class="filter-chip" onclick="filterNotifications('order')">Orders</span>
      <span class="filter-chip" onclick="filterNotifications('review')">Reviews</span>
      <span class="filter-chip" onclick="filterNotifications('system')">System</span>
    </div>
  </div>

  <!-- Notifications List -->
  {% if notifications %}
    <div id="notificationsList">
      {% for notification in notifications %}
        <div class="notification-item {% if not notification.is_read %}unread{% endif %}" 
             data-type="{{ notification.type }}" data-id="{{ notification.id }}">
          
          <!-- Notification Actions -->
          <div class="notification-actions">
            {% if not notification.is_read %}
              <button class="action-btn mark-read" onclick="markAsRead({{ notification.id }})" title="Mark as read">
                <i class="fas fa-check"></i>
              </button>
            {% endif %}
            <button class="action-btn delete" onclick="deleteNotification({{ notification.id }})" title="Delete">
              <i class="fas fa-trash"></i>
            </button>
          </div>

          <div class="card-body">
            <div class="d-flex align-items-start">
              <input type="checkbox" class="form-check-input me-3 mt-1" value="{{ notification.id }}">
              
              <div class="notification-icon {{ notification.type }}">
                {% if notification.type == 'price-alert' %}
                  <i class="fas fa-tag"></i>
                {% elif notification.type == 'deal' %}
                  <i class="fas fa-fire"></i>
                {% elif notification.type == 'order' %}
                  <i class="fas fa-shopping-bag"></i>
                {% elif notification.type == 'review' %}
                  <i class="fas fa-star"></i>
                {% elif notification.type == 'wishlist' %}
                  <i class="fas fa-heart"></i>
                {% else %}
                  <i class="fas fa-info"></i>
                {% endif %}
              </div>
              
              <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-text">{{ notification.message|truncatechars:150 }}</div>
                <div class="notification-time">
                  <i class="fas fa-clock me-1"></i>{{ notification.created_at|timesince }} ago
                </div>
              </div>
            </div>
            
            {% if notification.action_url %}
              <div class="mt-3">
                <a href="{{ notification.action_url }}" class="btn btn-primary btn-sm">
                  <i class="fas fa-external-link-alt me-1"></i>View Details
                </a>
              </div>
            {% endif %}
          </div>
        </div>
      {% endfor %}
    </div>

    <!-- Load More -->
    {% if has_more_notifications %}
      <div class="text-center mt-4">
        <button class="btn btn-outline-primary btn-lg" onclick="loadMoreNotifications()">
          <i class="fas fa-plus me-2"></i>Load More Notifications
        </button>
      </div>
    {% endif %}

  {% else %}
    <!-- Empty Notifications -->
    <div class="empty-notifications">
      <i class="fas fa-bell-slash fa-5x text-muted mb-4"></i>
      <h3>No Notifications</h3>
      <p class="text-muted mb-4">You're all caught up! We'll notify you when there's something new.</p>
      
      <div class="d-flex justify-content-center gap-3 mb-4">
        <a href="/products/" class="btn btn-primary btn-lg">
          <i class="fas fa-shopping-bag me-2"></i>Browse Products
        </a>
        <button class="btn btn-outline-primary btn-lg" onclick="toggleSettings()">
          <i class="fas fa-cog me-2"></i>Notification Settings
        </button>
      </div>

      <!-- Notification Types -->
      <div class="row mt-5">
        <div class="col-md-4">
          <div class="text-center">
            <i class="fas fa-tag fa-2x text-success mb-3"></i>
            <h6>Price Alerts</h6>
            <p class="text-muted small">Get notified when prices drop on your wishlist items</p>
          </div>
        </div>
        <div class="col-md-4">
          <div class="text-center">
            <i class="fas fa-fire fa-2x text-danger mb-3"></i>
            <h6>Deal Notifications</h6>
            <p class="text-muted small">Never miss flash sales and exclusive offers</p>
          </div>
        </div>
        <div class="col-md-4">
          <div class="text-center">
            <i class="fas fa-shopping-bag fa-2x text-primary mb-3"></i>
            <h6>Order Updates</h6>
            <p class="text-muted small">Track your orders from purchase to delivery</p>
          </div>
        </div>
      </div>
    </div>
  {% endif %}

  <!-- Notification Settings -->
  <div class="notification-settings" id="notificationSettings" style="display: none;">
    <h5 class="fw-bold mb-3">
      <i class="fas fa-cog me-2"></i>Notification Settings
    </h5>
    
    <div class="row">
      <div class="col-md-6">
        <h6 class="fw-semibold mb-3">Email Notifications</h6>
        
        <div class="setting-item">
          <div>
            <strong>Price Alerts</strong>
            <br><small class="text-muted">When prices drop on wishlist items</small>
          </div>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="emailPriceAlerts" checked>
          </div>
        </div>
        
        <div class="setting-item">
          <div>
            <strong>Deal Notifications</strong>
            <br><small class="text-muted">Flash sales and exclusive offers</small>
          </div>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="emailDeals" checked>
          </div>
        </div>
        
        <div class="setting-item">
          <div>
            <strong>Order Updates</strong>
            <br><small class="text-muted">Order confirmations and shipping updates</small>
          </div>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="emailOrders" checked>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <h6 class="fw-semibold mb-3">Push Notifications</h6>
        
        <div class="setting-item">
          <div>
            <strong>Instant Alerts</strong>
            <br><small class="text-muted">Real-time price drops and deals</small>
          </div>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="pushInstant">
          </div>
        </div>
        
        <div class="setting-item">
          <div>
            <strong>Daily Summary</strong>
            <br><small class="text-muted">Daily digest of new deals and updates</small>
          </div>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="pushDaily" checked>
          </div>
        </div>
        
        <div class="setting-item">
          <div>
            <strong>Weekly Newsletter</strong>
            <br><small class="text-muted">Weekly roundup of best deals</small>
          </div>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="pushWeekly" checked>
          </div>
        </div>
      </div>
    </div>
    
    <div class="text-center mt-4">
      <button class="btn btn-primary" onclick="saveNotificationSettings()">
        <i class="fas fa-save me-2"></i>Save Settings
      </button>
      <button class="btn btn-outline-secondary ms-2" onclick="toggleSettings()">
        <i class="fas fa-times me-2"></i>Close
      </button>
    </div>
  </div>
</div>

<script>
function filterNotifications(type) {
  // Remove active class from all chips
  document.querySelectorAll('.filter-chip').forEach(chip => {
    chip.classList.remove('active');
  });
  
  // Add active class to clicked chip
  event.target.classList.add('active');
  
  // Filter notifications
  const notifications = document.querySelectorAll('.notification-item');
  notifications.forEach(notification => {
    if (type === 'all') {
      notification.style.display = 'block';
    } else if (type === 'unread') {
      notification.style.display = notification.classList.contains('unread') ? 'block' : 'none';
    } else {
      notification.style.display = notification.dataset.type === type ? 'block' : 'none';
    }
  });
}

function markAsRead(notificationId) {
  console.log('Marking as read:', notificationId);
  
  const notification = document.querySelector(`[data-id="${notificationId}"]`);
  notification.classList.remove('unread');
  
  // Remove the mark as read button
  const markReadBtn = notification.querySelector('.mark-read');
  if (markReadBtn) {
    markReadBtn.remove();
  }
  
  showToast('Notification marked as read', 'success');
  updateNotificationStats();
}

function deleteNotification(notificationId) {
  if (confirm('Are you sure you want to delete this notification?')) {
    console.log('Deleting notification:', notificationId);
    
    const notification = document.querySelector(`[data-id="${notificationId}"]`);
    notification.style.opacity = '0';
    notification.style.transform = 'scale(0.8)';
    
    setTimeout(() => {
      notification.remove();
      updateNotificationStats();
    }, 300);
    
    showToast('Notification deleted', 'success');
  }
}

function markAllAsRead() {
  if (confirm('Mark all notifications as read?')) {
    document.querySelectorAll('.notification-item.unread').forEach(notification => {
      notification.classList.remove('unread');
      const markReadBtn = notification.querySelector('.mark-read');
      if (markReadBtn) {
        markReadBtn.remove();
      }
    });
    
    showToast('All notifications marked as read', 'success');
    updateNotificationStats();
  }
}

function selectAll() {
  const checkboxes = document.querySelectorAll('.notification-item input[type="checkbox"]');
  const allChecked = Array.from(checkboxes).every(cb => cb.checked);
  
  checkboxes.forEach(checkbox => {
    checkbox.checked = !allChecked;
  });
}

function deleteSelected() {
  const selectedCheckboxes = document.querySelectorAll('.notification-item input[type="checkbox"]:checked');
  
  if (selectedCheckboxes.length === 0) {
    showToast('Please select notifications to delete', 'warning');
    return;
  }
  
  if (confirm(`Delete ${selectedCheckboxes.length} selected notifications?`)) {
    selectedCheckboxes.forEach(checkbox => {
      const notification = checkbox.closest('.notification-item');
      notification.style.opacity = '0';
      notification.style.transform = 'scale(0.8)';
      
      setTimeout(() => {
        notification.remove();
      }, 300);
    });
    
    showToast(`${selectedCheckboxes.length} notifications deleted`, 'success');
    updateNotificationStats();
  }
}

function toggleSettings() {
  const settings = document.getElementById('notificationSettings');
  if (settings.style.display === 'none') {
    settings.style.display = 'block';
    settings.scrollIntoView({ behavior: 'smooth' });
  } else {
    settings.style.display = 'none';
  }
}

function saveNotificationSettings() {
  console.log('Saving notification settings...');
  showToast('Notification settings saved!', 'success');
  toggleSettings();
}

function loadMoreNotifications() {
  console.log('Loading more notifications...');
  showToast('Loading more notifications...', 'info');
}

function updateNotificationStats() {
  const totalNotifications = document.querySelectorAll('.notification-item').length;
  const unreadCount = document.querySelectorAll('.notification-item.unread').length;
  
  // Update stats display
  document.querySelector('.stat-number').textContent = totalNotifications;
  document.querySelectorAll('.stat-number')[1].textContent = unreadCount;
}

function showToast(message, type) {
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} position-fixed`;
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
  toast.innerHTML = `
    ${message}
    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
  `;
  document.body.appendChild(toast);
  
  setTimeout(() => {
    if (toast.parentElement) {
      toast.remove();
    }
  }, 3000);
}

// Auto-refresh notifications every 30 seconds
setInterval(() => {
  console.log('Auto-refreshing notifications...');
  // This would make an AJAX call to check for new notifications
}, 30000);
</script>
{% endblock %}
