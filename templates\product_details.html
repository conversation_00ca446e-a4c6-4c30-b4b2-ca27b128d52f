{% extends 'base.html' %}
{% load static %}

{% block title %}{{ product.name }} - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="{{ product.description|truncatechars:160 }}">
<meta property="og:title" content="{{ product.name }} - Best in Click">
<meta property="og:description" content="{{ product.description|truncatechars:160 }}">
<meta property="og:image" content="{{ product.image_url }}">
<meta property="og:type" content="product">
<style>
  .breadcrumb-custom {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
  }
  .product-image-main {
    height: 400px;
    object-fit: cover;
    border-radius: 1rem;
    cursor: zoom-in;
  }
  .product-thumbnail {
    height: 80px;
    object-fit: cover;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  .product-thumbnail:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
  }
  .product-thumbnail.active {
    border: 2px solid #0d6efd;
  }
  .price-section {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }
  .current-price {
    font-size: 2rem;
    font-weight: bold;
    color: #0d6efd;
  }
  .original-price {
    font-size: 1.2rem;
    text-decoration: line-through;
    color: #6c757d;
  }
  .discount-badge {
    background: #dc3545;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: bold;
  }
  .rating-section {
    background: #fff3cd;
    border-radius: 1rem;
    padding: 1rem;
    margin-bottom: 1rem;
  }
  .rating-stars {
    color: #ffc107;
    font-size: 1.5rem;
  }
  .feature-list {
    list-style: none;
    padding: 0;
  }
  .feature-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
  }
  .feature-list li:last-child {
    border-bottom: none;
  }
  .feature-list li i {
    color: #28a745;
    margin-right: 0.5rem;
  }
  .review-card {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
  }
  .review-rating {
    color: #ffc107;
  }
  .similar-product {
    transition: transform 0.3s ease;
  }
  .similar-product:hover {
    transform: translateY(-5px);
  }
  .sticky-actions {
    position: sticky;
    top: 100px;
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  }
  .zoom-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1050;
  }
  .zoom-image {
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
  }
  .specification-table {
    background: #f8f9fa;
    border-radius: 1rem;
    overflow: hidden;
  }
  .specification-table th {
    background: #e9ecef;
    font-weight: 600;
    color: #495057;
  }
</style>
{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<div class="container mt-3">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-custom">
      <li class="breadcrumb-item"><a href="/">Home</a></li>
      <li class="breadcrumb-item"><a href="/products/">Products</a></li>
      {% if product.category %}
        <li class="breadcrumb-item"><a href="/products/?category={{ product.category.id }}">{{ product.category.name }}</a></li>
      {% endif %}
      <li class="breadcrumb-item active">{{ product.name|truncatechars:30 }}</li>
    </ol>
  </nav>
</div>

<div class="container">
  <div class="row">
    <!-- Product Images -->
    <div class="col-lg-6 mb-4">
      <div class="mb-3">
        {% if product.image_url %}
          <img src="{{ product.image_url }}" class="img-fluid product-image-main" alt="{{ product.name }}" id="mainImage" onclick="zoomImage(this.src)">
        {% else %}
          <div class="product-image-main bg-light d-flex align-items-center justify-content-center">
            <i class="fas fa-image fa-5x text-muted"></i>
          </div>
        {% endif %}
      </div>
      
      <!-- Thumbnail Images -->
      <div class="row">
        {% if product.image_url %}
          <div class="col-3">
            <img src="{{ product.image_url }}" class="img-fluid product-thumbnail active" alt="{{ product.name }}" onclick="changeMainImage(this.src)">
          </div>
        {% endif %}
        <!-- Additional product images would be displayed here -->
        <div class="col-3">
          <div class="product-thumbnail bg-light d-flex align-items-center justify-content-center">
            <i class="fas fa-plus text-muted"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Product Info -->
    <div class="col-lg-6">
      <h1 class="fw-bold mb-3">{{ product.name }}</h1>
      
      <!-- Rating -->
      <div class="rating-section">
        <div class="d-flex align-items-center">
          <div class="rating-stars me-3">
            {% for i in "12345" %}
              {% if forloop.counter <= product.rating %}★{% else %}☆{% endif %}
            {% endfor %}
          </div>
          <span class="fw-semibold">{{ product.rating }}/5</span>
          <span class="text-muted ms-2">({{ product.review_count }} reviews)</span>
        </div>
      </div>

      <!-- Price -->
      <div class="price-section">
        <div class="d-flex align-items-center justify-content-between">
          <div>
            <span class="current-price">${{ product.price }}</span>
            {% if product.original_price and product.original_price != product.price %}
              <span class="original-price ms-3">${{ product.original_price }}</span>
            {% endif %}
          </div>
          {% if product.discount_percentage %}
            <span class="discount-badge">Save {{ product.discount_percentage }}%</span>
          {% endif %}
        </div>
      </div>

      <!-- Product Info -->
      <div class="mb-4">
        <h5 class="fw-bold">Product Details</h5>
        <p class="text-muted">{{ product.description }}</p>
        
        <ul class="feature-list">
          {% if product.brand %}
            <li><i class="fas fa-check"></i><strong>Brand:</strong> {{ product.brand.name }}</li>
          {% endif %}
          {% if product.category %}
            <li><i class="fas fa-check"></i><strong>Category:</strong> {{ product.category.name }}</li>
          {% endif %}
          {% if product.sku %}
            <li><i class="fas fa-check"></i><strong>SKU:</strong> {{ product.sku }}</li>
          {% endif %}
          <li><i class="fas fa-check"></i><strong>Availability:</strong> 
            {% if product.is_available %}
              <span class="text-success">In Stock</span>
            {% else %}
              <span class="text-danger">Out of Stock</span>
            {% endif %}
          </li>
          {% if product.stock_quantity %}
            <li><i class="fas fa-check"></i><strong>Stock:</strong> {{ product.stock_quantity }} units available</li>
          {% endif %}
        </ul>
      </div>

      <!-- Store Info -->
      {% if product.shop %}
      <div class="card mb-4">
        <div class="card-body">
          <h6 class="card-title">
            <i class="fas fa-store me-2"></i>Sold by {{ product.shop.name }}
          </h6>
          <div class="d-flex align-items-center">
            <div class="rating-stars me-2">
              {% for i in "12345" %}
                {% if forloop.counter <= product.shop.rating %}★{% else %}☆{% endif %}
              {% endfor %}
            </div>
            <span class="text-muted">Store Rating</span>
          </div>
          <a href="/stores/{{ product.shop.id }}/" class="btn btn-outline-primary btn-sm mt-2">
            <i class="fas fa-external-link-alt me-1"></i>Visit Store
          </a>
        </div>
      </div>
      {% endif %}

      <!-- Action Buttons -->
      <div class="sticky-actions">
        <div class="d-grid gap-2">
          <button class="btn btn-primary btn-lg" onclick="buyNow()">
            <i class="fas fa-shopping-cart me-2"></i>Buy Now
          </button>
          <div class="row">
            <div class="col-6">
              <button class="btn btn-outline-primary w-100" onclick="addToWishlist({{ product.id }})">
                <i class="fas fa-heart me-1"></i>Wishlist
              </button>
            </div>
            <div class="col-6">
              <button class="btn btn-outline-primary w-100" onclick="addToCompare({{ product.id }})">
                <i class="fas fa-balance-scale me-1"></i>Compare
              </button>
            </div>
          </div>
          <button class="btn btn-outline-secondary" onclick="shareProduct()">
            <i class="fas fa-share-alt me-2"></i>Share Product
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Product Tabs -->
  <div class="row mt-5">
    <div class="col-12">
      <ul class="nav nav-tabs" id="productTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="description-tab" data-bs-toggle="tab" data-bs-target="#description" type="button">
            Description
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="specifications-tab" data-bs-toggle="tab" data-bs-target="#specifications" type="button">
            Specifications
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button">
            Reviews ({{ product.review_count }})
          </button>
        </li>
      </ul>
      
      <div class="tab-content" id="productTabsContent">
        <!-- Description Tab -->
        <div class="tab-pane fade show active" id="description" role="tabpanel">
          <div class="p-4">
            <h5>Product Description</h5>
            <p>{{ product.description }}</p>
            
            <!-- Additional product details would go here -->
            <h6>Key Features:</h6>
            <ul>
              <li>High-quality materials and construction</li>
              <li>Designed for durability and performance</li>
              <li>Easy to use and maintain</li>
              <li>Backed by manufacturer warranty</li>
            </ul>
          </div>
        </div>
        
        <!-- Specifications Tab -->
        <div class="tab-pane fade" id="specifications" role="tabpanel">
          <div class="p-4">
            <h5>Technical Specifications</h5>
            <table class="table specification-table">
              <tbody>
                {% if product.brand %}
                <tr>
                  <th width="30%">Brand</th>
                  <td>{{ product.brand.name }}</td>
                </tr>
                {% endif %}
                {% if product.sku %}
                <tr>
                  <th>Model/SKU</th>
                  <td>{{ product.sku }}</td>
                </tr>
                {% endif %}
                <tr>
                  <th>Category</th>
                  <td>{{ product.category.name }}</td>
                </tr>
                <tr>
                  <th>Availability</th>
                  <td>
                    {% if product.is_available %}
                      <span class="badge bg-success">In Stock</span>
                    {% else %}
                      <span class="badge bg-danger">Out of Stock</span>
                    {% endif %}
                  </td>
                </tr>
                <!-- Additional specifications would be dynamically loaded here -->
              </tbody>
            </table>
          </div>
        </div>
        
        <!-- Reviews Tab -->
        <div class="tab-pane fade" id="reviews" role="tabpanel">
          <div class="p-4">
            <div class="d-flex justify-content-between align-items-center mb-4">
              <h5>Customer Reviews</h5>
              <button class="btn btn-primary" onclick="writeReview()">Write a Review</button>
            </div>
            
            <!-- Reviews would be loaded here -->
            <div class="review-card">
              <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                  <div>
                    <h6 class="mb-1">John Doe</h6>
                    <div class="review-rating">★★★★★</div>
                  </div>
                  <small class="text-muted">2 days ago</small>
                </div>
                <p class="mb-0">Excellent product! Exactly as described and arrived quickly. Highly recommend!</p>
              </div>
            </div>
            
            <div class="text-center mt-4">
              <button class="btn btn-outline-primary" onclick="loadMoreReviews()">Load More Reviews</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Similar Products -->
  <div class="row mt-5">
    <div class="col-12">
      <h3 class="fw-bold mb-4">Similar Products</h3>
      <div class="row">
        <!-- Similar products would be loaded here -->
        <div class="col-lg-3 col-md-6 mb-4">
          <div class="card similar-product h-100">
            <div class="card-body text-center">
              <div class="bg-light rounded p-3 mb-3">
                <i class="fas fa-image fa-2x text-muted"></i>
              </div>
              <h6 class="card-title">Similar Product 1</h6>
              <p class="text-primary fw-bold">$99.99</p>
              <button class="btn btn-outline-primary btn-sm">View Details</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Image Zoom Overlay -->
<div class="zoom-overlay" id="zoomOverlay" onclick="closeZoom()">
  <img src="" alt="Zoomed product image" class="zoom-image" id="zoomImage">
</div>

<script>
function changeMainImage(src) {
  document.getElementById('mainImage').src = src;
  
  // Update active thumbnail
  document.querySelectorAll('.product-thumbnail').forEach(thumb => {
    thumb.classList.remove('active');
  });
  event.target.classList.add('active');
}

function zoomImage(src) {
  document.getElementById('zoomImage').src = src;
  document.getElementById('zoomOverlay').style.display = 'flex';
}

function closeZoom() {
  document.getElementById('zoomOverlay').style.display = 'none';
}

function buyNow() {
  // Redirect to store or implement buy now functionality
  {% if product.product_url %}
    window.open('{{ product.product_url }}', '_blank');
  {% else %}
    alert('Buy now functionality would be implemented here');
  {% endif %}
}

function addToWishlist(productId) {
  // Wishlist functionality
  console.log('Add to wishlist:', productId);
  // Show success message
  showToast('Added to wishlist!', 'success');
}

function addToCompare(productId) {
  // Compare functionality
  console.log('Add to compare:', productId);
  showToast('Added to comparison!', 'info');
}

function shareProduct() {
  if (navigator.share) {
    navigator.share({
      title: '{{ product.name }}',
      text: '{{ product.description|truncatechars:100 }}',
      url: window.location.href
    });
  } else {
    // Fallback: copy to clipboard
    navigator.clipboard.writeText(window.location.href);
    showToast('Link copied to clipboard!', 'success');
  }
}

function writeReview() {
  // Open review modal or redirect to review page
  alert('Review functionality would be implemented here');
}

function loadMoreReviews() {
  // Load more reviews via AJAX
  console.log('Loading more reviews...');
}

function showToast(message, type) {
  // Simple toast notification
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} position-fixed`;
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
  toast.innerHTML = `
    ${message}
    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
  `;
  document.body.appendChild(toast);
  
  setTimeout(() => {
    if (toast.parentElement) {
      toast.remove();
    }
  }, 3000);
}

// Close zoom on Escape key
document.addEventListener('keydown', function(e) {
  if (e.key === 'Escape') {
    closeZoom();
  }
});
</script>
{% endblock %}
