{% extends 'base.html' %}
{% load static %}

{% block title %}Products - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="Discover millions of products from thousands of stores. Compare prices, read reviews, and find the best deals with Best in Click.">
<style>
  .products-hero {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
  }
  .filter-sidebar {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    height: fit-content;
    position: sticky;
    top: 100px;
  }
  .product-card {
    border: none;
    border-radius: 1rem;
    transition: all 0.3s ease;
    height: 100%;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }
  .product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.15);
  }
  .product-image {
    height: 200px;
    object-fit: cover;
    border-radius: 1rem 1rem 0 0;
  }
  .product-price {
    font-size: 1.25rem;
    font-weight: bold;
    color: #0d6efd;
  }
  .product-original-price {
    text-decoration: line-through;
    color: #6c757d;
    font-size: 0.9rem;
  }
  .discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #dc3545;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.5rem;
    font-size: 0.8rem;
    font-weight: bold;
  }
  .rating-stars {
    color: #ffc107;
  }
  .filter-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #dee2e6;
  }
  .filter-section:last-child {
    border-bottom: none;
  }
  .sort-dropdown {
    min-width: 200px;
  }
  .view-toggle {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
  }
  .view-toggle .btn {
    border: none;
    background: transparent;
  }
  .view-toggle .btn.active {
    background: #0d6efd;
    color: white;
  }
  .pagination-custom .page-link {
    border-radius: 0.5rem;
    margin: 0 0.25rem;
    border: 1px solid #dee2e6;
  }
  .pagination-custom .page-item.active .page-link {
    background: #0d6efd;
    border-color: #0d6efd;
  }
  .loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }
  @keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="products-hero">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-8">
        <h1 class="display-5 fw-bold mb-3">Discover Amazing Products</h1>
        <p class="lead mb-0">Browse millions of products from thousands of trusted stores worldwide</p>
      </div>
      <div class="col-lg-4 text-center">
        <i class="fas fa-shopping-bag" style="font-size: 4rem; opacity: 0.3;"></i>
      </div>
    </div>
  </div>
</div>

<div class="container">
  <!-- Search and Filters Bar -->
  <div class="row mb-4">
    <div class="col-lg-6">
      <form method="get" class="d-flex">
        <input type="text" name="q" class="form-control me-2" placeholder="Search products..." value="{{ request.GET.q }}">
        <button class="btn btn-primary" type="submit">
          <i class="fas fa-search"></i>
        </button>
      </form>
    </div>
    <div class="col-lg-3">
      <select class="form-select sort-dropdown" name="sort" onchange="updateSort(this.value)">
        <option value="">Sort by...</option>
        <option value="name" {% if request.GET.sort == 'name' %}selected{% endif %}>Name A-Z</option>
        <option value="-name" {% if request.GET.sort == '-name' %}selected{% endif %}>Name Z-A</option>
        <option value="price" {% if request.GET.sort == 'price' %}selected{% endif %}>Price Low to High</option>
        <option value="-price" {% if request.GET.sort == '-price' %}selected{% endif %}>Price High to Low</option>
        <option value="-rating" {% if request.GET.sort == '-rating' %}selected{% endif %}>Highest Rated</option>
        <option value="-created_at" {% if request.GET.sort == '-created_at' %}selected{% endif %}>Newest First</option>
      </select>
    </div>
    <div class="col-lg-3">
      <div class="view-toggle btn-group w-100">
        <button type="button" class="btn active" onclick="setView('grid')">
          <i class="fas fa-th"></i> Grid
        </button>
        <button type="button" class="btn" onclick="setView('list')">
          <i class="fas fa-list"></i> List
        </button>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Filters Sidebar -->
    <div class="col-lg-3">
      <div class="filter-sidebar">
        <h5 class="fw-bold mb-3">Filters</h5>
        
        <!-- Categories Filter -->
        <div class="filter-section">
          <h6 class="fw-semibold">Categories</h6>
          {% for category in categories %}
          <div class="form-check">
            <input class="form-check-input" type="checkbox" name="category" value="{{ category.id }}" id="cat{{ category.id }}"
                   {% if category.id|stringformat:"s" in request.GET.category %}checked{% endif %}>
            <label class="form-check-label" for="cat{{ category.id }}">
              {{ category.name }} ({{ category.product_count }})
            </label>
          </div>
          {% endfor %}
        </div>

        <!-- Price Range Filter -->
        <div class="filter-section">
          <h6 class="fw-semibold">Price Range</h6>
          <div class="row">
            <div class="col-6">
              <input type="number" class="form-control form-control-sm" placeholder="Min" name="min_price" value="{{ request.GET.min_price }}">
            </div>
            <div class="col-6">
              <input type="number" class="form-control form-control-sm" placeholder="Max" name="max_price" value="{{ request.GET.max_price }}">
            </div>
          </div>
        </div>

        <!-- Brands Filter -->
        <div class="filter-section">
          <h6 class="fw-semibold">Brands</h6>
          {% for brand in brands %}
          <div class="form-check">
            <input class="form-check-input" type="checkbox" name="brand" value="{{ brand.id }}" id="brand{{ brand.id }}"
                   {% if brand.id|stringformat:"s" in request.GET.brand %}checked{% endif %}>
            <label class="form-check-label" for="brand{{ brand.id }}">
              {{ brand.name }}
            </label>
          </div>
          {% endfor %}
        </div>

        <!-- Rating Filter -->
        <div class="filter-section">
          <h6 class="fw-semibold">Rating</h6>
          <div class="form-check">
            <input class="form-check-input" type="radio" name="rating" value="4" id="rating4">
            <label class="form-check-label" for="rating4">
              <span class="rating-stars">★★★★☆</span> & up
            </label>
          </div>
          <div class="form-check">
            <input class="form-check-input" type="radio" name="rating" value="3" id="rating3">
            <label class="form-check-label" for="rating3">
              <span class="rating-stars">★★★☆☆</span> & up
            </label>
          </div>
        </div>

        <!-- Availability Filter -->
        <div class="filter-section">
          <h6 class="fw-semibold">Availability</h6>
          <div class="form-check">
            <input class="form-check-input" type="checkbox" name="in_stock" value="1" id="inStock">
            <label class="form-check-label" for="inStock">
              In Stock Only
            </label>
          </div>
          <div class="form-check">
            <input class="form-check-input" type="checkbox" name="on_sale" value="1" id="onSale">
            <label class="form-check-label" for="onSale">
              On Sale
            </label>
          </div>
        </div>

        <button class="btn btn-primary w-100 mb-2" onclick="applyFilters()">Apply Filters</button>
        <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">Clear All</button>
      </div>
    </div>

    <!-- Products Grid -->
    <div class="col-lg-9">
      <!-- Results Info -->
      <div class="d-flex justify-content-between align-items-center mb-3">
        <p class="text-muted mb-0">
          Showing {{ products.start_index }}-{{ products.end_index }} of {{ products.paginator.count }} results
          {% if request.GET.q %} for "{{ request.GET.q }}"{% endif %}
        </p>
        <div class="d-flex gap-2">
          <button class="btn btn-outline-primary btn-sm" onclick="toggleWishlist()">
            <i class="fas fa-heart"></i> Wishlist
          </button>
          <button class="btn btn-outline-primary btn-sm" onclick="toggleCompare()">
            <i class="fas fa-balance-scale"></i> Compare
          </button>
        </div>
      </div>

      <!-- Products Grid -->
      <div class="row" id="productsGrid">
        {% for product in products %}
        <div class="col-lg-4 col-md-6 mb-4">
          <div class="card product-card">
            <div class="position-relative">
              {% if product.image_url %}
                <img src="{{ product.image_url }}" class="card-img-top product-image" alt="{{ product.name }}">
              {% else %}
                <div class="product-image bg-light d-flex align-items-center justify-content-center">
                  <i class="fas fa-image fa-3x text-muted"></i>
                </div>
              {% endif %}
              
              {% if product.discount_percentage %}
                <span class="discount-badge">-{{ product.discount_percentage }}%</span>
              {% endif %}
              
              <div class="position-absolute top-0 start-0 p-2">
                <button class="btn btn-sm btn-light rounded-circle" onclick="toggleWishlist({{ product.id }})">
                  <i class="fas fa-heart"></i>
                </button>
              </div>
            </div>
            
            <div class="card-body">
              <h6 class="card-title">
                <a href="/products/{{ product.id }}/" class="text-decoration-none text-dark">
                  {{ product.name|truncatechars:50 }}
                </a>
              </h6>
              
              <p class="card-text text-muted small">{{ product.description|truncatechars:80 }}</p>
              
              <div class="d-flex align-items-center mb-2">
                <div class="rating-stars me-2">
                  {% for i in "12345" %}
                    {% if forloop.counter <= product.rating %}★{% else %}☆{% endif %}
                  {% endfor %}
                </div>
                <small class="text-muted">({{ product.review_count }} reviews)</small>
              </div>
              
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <span class="product-price">${{ product.price }}</span>
                  {% if product.original_price and product.original_price != product.price %}
                    <span class="product-original-price ms-2">${{ product.original_price }}</span>
                  {% endif %}
                </div>
                <small class="text-muted">{{ product.shop.name }}</small>
              </div>
            </div>
            
            <div class="card-footer bg-transparent">
              <div class="d-flex gap-2">
                <a href="/products/{{ product.id }}/" class="btn btn-primary btn-sm flex-fill">
                  <i class="fas fa-eye me-1"></i>View Details
                </a>
                <button class="btn btn-outline-primary btn-sm" onclick="addToCompare({{ product.id }})">
                  <i class="fas fa-balance-scale"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
        {% empty %}
        <div class="col-12">
          <div class="text-center py-5">
            <i class="fas fa-search fa-4x text-muted mb-3"></i>
            <h4>No products found</h4>
            <p class="text-muted">Try adjusting your search criteria or filters</p>
            <a href="/products/" class="btn btn-primary">View All Products</a>
          </div>
        </div>
        {% endfor %}
      </div>

      <!-- Pagination -->
      {% if products.has_other_pages %}
      <nav aria-label="Products pagination">
        <ul class="pagination pagination-custom justify-content-center">
          {% if products.has_previous %}
            <li class="page-item">
              <a class="page-link" href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}page={{ products.previous_page_number }}">
                <i class="fas fa-chevron-left"></i>
              </a>
            </li>
          {% endif %}
          
          {% for num in products.paginator.page_range %}
            {% if products.number == num %}
              <li class="page-item active">
                <span class="page-link">{{ num }}</span>
              </li>
            {% elif num > products.number|add:'-3' and num < products.number|add:'3' %}
              <li class="page-item">
                <a class="page-link" href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}page={{ num }}">{{ num }}</a>
              </li>
            {% endif %}
          {% endfor %}
          
          {% if products.has_next %}
            <li class="page-item">
              <a class="page-link" href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}page={{ products.next_page_number }}">
                <i class="fas fa-chevron-right"></i>
              </a>
            </li>
          {% endif %}
        </ul>
      </nav>
      {% endif %}
    </div>
  </div>
</div>

<script>
// Filter and sort functions
function updateSort(value) {
  const url = new URL(window.location);
  if (value) {
    url.searchParams.set('sort', value);
  } else {
    url.searchParams.delete('sort');
  }
  window.location.href = url.toString();
}

function applyFilters() {
  const form = document.createElement('form');
  form.method = 'GET';
  
  // Collect all filter values
  const filters = document.querySelectorAll('.filter-sidebar input:checked, .filter-sidebar input[type="number"]');
  filters.forEach(filter => {
    if (filter.value) {
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = filter.name;
      input.value = filter.value;
      form.appendChild(input);
    }
  });
  
  // Preserve search query
  const searchQuery = document.querySelector('input[name="q"]').value;
  if (searchQuery) {
    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = 'q';
    input.value = searchQuery;
    form.appendChild(input);
  }
  
  document.body.appendChild(form);
  form.submit();
}

function clearFilters() {
  const url = new URL(window.location);
  const searchQuery = url.searchParams.get('q');
  url.search = '';
  if (searchQuery) {
    url.searchParams.set('q', searchQuery);
  }
  window.location.href = url.toString();
}

function setView(viewType) {
  // Toggle view buttons
  document.querySelectorAll('.view-toggle .btn').forEach(btn => btn.classList.remove('active'));
  event.target.closest('.btn').classList.add('active');
  
  // Update grid layout
  const grid = document.getElementById('productsGrid');
  if (viewType === 'list') {
    grid.classList.add('list-view');
  } else {
    grid.classList.remove('list-view');
  }
}

function toggleWishlist(productId) {
  // Wishlist functionality would be implemented here
  console.log('Toggle wishlist for product:', productId);
}

function addToCompare(productId) {
  // Compare functionality would be implemented here
  console.log('Add to compare:', productId);
}

// Loading states
function showLoading() {
  document.getElementById('productsGrid').innerHTML = `
    <div class="col-12">
      <div class="d-flex justify-content-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>
    </div>
  `;
}
</script>
{% endblock %}
