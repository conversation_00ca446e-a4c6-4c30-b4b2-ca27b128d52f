{% extends 'base.html' %}
{% load static %}

{% block title %}{{ user.get_full_name|default:user.username }} - Profile - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="View and manage your Best in Click profile, shopping history, wishlist, and account settings.">
<style>
  .profile-header {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    color: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
  }
  .profile-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="white" opacity="0.1"/></svg>') repeat;
    animation: float 20s infinite linear;
  }
  @keyframes float {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(-50px) translateY(-50px); }
  }
  .profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid white;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    object-fit: cover;
  }
  .profile-stats {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
  }
  .stat-item {
    text-align: center;
    padding: 1rem;
  }
  .stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #0d6efd;
    display: block;
  }
  .stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.5rem;
  }
  .activity-card {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
  }
  .activity-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.15);
  }
  .activity-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    margin-right: 1rem;
  }
  .activity-icon.purchase { background: #28a745; }
  .activity-icon.review { background: #ffc107; color: #000; }
  .activity-icon.wishlist { background: #dc3545; }
  .activity-icon.compare { background: #17a2b8; }
  .quick-actions {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  .action-btn {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    text-decoration: none;
    color: #495057;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
  }
  .action-btn:hover {
    background: #e9ecef;
    color: #0d6efd;
    text-decoration: none;
    transform: translateX(5px);
  }
  .action-btn i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
  }
  .badge-collection {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
  }
  .achievement-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #000;
    border-radius: 2rem;
    padding: 0.5rem 1rem;
    margin: 0.25rem;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
  }
  .achievement-badge i {
    margin-right: 0.5rem;
  }
  .recent-activity {
    max-height: 400px;
    overflow-y: auto;
  }
  .activity-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #f8f9fa;
  }
  .activity-item:last-child {
    border-bottom: none;
  }
  .activity-content {
    flex-grow: 1;
  }
  .activity-time {
    color: #6c757d;
    font-size: 0.8rem;
  }
  .profile-completion {
    background: linear-gradient(45deg, #e3f0ff, #f8f9fa);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  .completion-bar {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin: 1rem 0;
  }
  .completion-fill {
    height: 100%;
    background: linear-gradient(90deg, #0d6efd, #0056b3);
    border-radius: 4px;
    transition: width 0.3s ease;
  }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
  <!-- Profile Header -->
  <div class="profile-header">
    <div class="row align-items-center">
      <div class="col-md-3 text-center">
        {% if user.profile.avatar %}
          <img src="{{ user.profile.avatar.url }}" class="profile-avatar" alt="{{ user.get_full_name }}">
        {% else %}
          <div class="profile-avatar bg-light d-flex align-items-center justify-content-center mx-auto">
            <i class="fas fa-user fa-3x text-muted"></i>
          </div>
        {% endif %}
      </div>
      
      <div class="col-md-6">
        <h1 class="fw-bold mb-2">{{ user.get_full_name|default:user.username }}</h1>
        <p class="mb-2">
          <i class="fas fa-envelope me-2"></i>{{ user.email }}
        </p>
        {% if user.profile.location %}
          <p class="mb-2">
            <i class="fas fa-map-marker-alt me-2"></i>{{ user.profile.location }}
          </p>
        {% endif %}
        <p class="mb-0">
          <i class="fas fa-calendar me-2"></i>Member since {{ user.date_joined|date:"F Y" }}
        </p>
      </div>
      
      <div class="col-md-3 text-end">
        <a href="/profile/edit/" class="btn btn-light btn-lg mb-2">
          <i class="fas fa-edit me-2"></i>Edit Profile
        </a>
        <br>
        <a href="/account/settings/" class="btn btn-outline-light">
          <i class="fas fa-cog me-2"></i>Settings
        </a>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Left Column -->
    <div class="col-lg-8">
      <!-- Profile Completion -->
      <div class="profile-completion">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <h5 class="fw-bold mb-0">Profile Completion</h5>
          <span class="badge bg-primary">{{ profile_completion }}%</span>
        </div>
        <div class="completion-bar">
          <div class="completion-fill" style="width: {{ profile_completion }}%;"></div>
        </div>
        <small class="text-muted">
          Complete your profile to get better recommendations and exclusive offers
        </small>
      </div>

      <!-- Statistics -->
      <div class="profile-stats">
        <div class="row">
          <div class="col-md-3">
            <div class="stat-item">
              <span class="stat-number">{{ user.orders.count }}</span>
              <div class="stat-label">Orders</div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="stat-item">
              <span class="stat-number">{{ user.reviews.count }}</span>
              <div class="stat-label">Reviews</div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="stat-item">
              <span class="stat-number">{{ user.wishlist_items.count }}</span>
              <div class="stat-label">Wishlist Items</div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="stat-item">
              <span class="stat-number">{{ user.profile.points|default:0 }}</span>
              <div class="stat-label">Reward Points</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Achievements -->
      <div class="badge-collection">
        <h5 class="fw-bold mb-3">
          <i class="fas fa-trophy me-2"></i>Achievements
        </h5>
        
        {% if user.orders.count >= 1 %}
          <span class="achievement-badge">
            <i class="fas fa-shopping-cart"></i>First Purchase
          </span>
        {% endif %}
        
        {% if user.reviews.count >= 5 %}
          <span class="achievement-badge">
            <i class="fas fa-star"></i>Review Master
          </span>
        {% endif %}
        
        {% if user.wishlist_items.count >= 10 %}
          <span class="achievement-badge">
            <i class="fas fa-heart"></i>Wishlist Collector
          </span>
        {% endif %}
        
        {% if user.profile.points >= 1000 %}
          <span class="achievement-badge">
            <i class="fas fa-gem"></i>VIP Member
          </span>
        {% endif %}
        
        {% if not user.orders.count and not user.reviews.count %}
          <p class="text-muted mb-0">
            <i class="fas fa-info-circle me-2"></i>
            Start shopping and reviewing products to earn achievements!
          </p>
        {% endif %}
      </div>

      <!-- Recent Activity -->
      <div class="activity-card">
        <div class="card-header bg-transparent">
          <h5 class="fw-bold mb-0">
            <i class="fas fa-clock me-2"></i>Recent Activity
          </h5>
        </div>
        <div class="card-body p-0">
          <div class="recent-activity">
            {% for activity in recent_activities %}
              <div class="activity-item">
                <div class="activity-icon {{ activity.type }}">
                  {% if activity.type == 'purchase' %}
                    <i class="fas fa-shopping-cart"></i>
                  {% elif activity.type == 'review' %}
                    <i class="fas fa-star"></i>
                  {% elif activity.type == 'wishlist' %}
                    <i class="fas fa-heart"></i>
                  {% elif activity.type == 'compare' %}
                    <i class="fas fa-balance-scale"></i>
                  {% endif %}
                </div>
                <div class="activity-content">
                  <h6 class="mb-1">{{ activity.title }}</h6>
                  <p class="text-muted mb-0">{{ activity.description }}</p>
                  <div class="activity-time">{{ activity.created_at|timesince }} ago</div>
                </div>
              </div>
            {% empty %}
              <div class="text-center py-4">
                <i class="fas fa-history fa-3x text-muted mb-3"></i>
                <h6>No Recent Activity</h6>
                <p class="text-muted">Start shopping to see your activity here!</p>
                <a href="/products/" class="btn btn-primary">Browse Products</a>
              </div>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>

    <!-- Right Column -->
    <div class="col-lg-4">
      <!-- Quick Actions -->
      <div class="quick-actions">
        <h5 class="fw-bold mb-3">Quick Actions</h5>
        
        <a href="/wishlist/" class="action-btn">
          <i class="fas fa-heart"></i>
          <div>
            <strong>My Wishlist</strong>
            <br><small class="text-muted">{{ user.wishlist_items.count }} items saved</small>
          </div>
        </a>
        
        <a href="/reviews/" class="action-btn">
          <i class="fas fa-star"></i>
          <div>
            <strong>My Reviews</strong>
            <br><small class="text-muted">{{ user.reviews.count }} reviews written</small>
          </div>
        </a>
        
        <a href="/orders/" class="action-btn">
          <i class="fas fa-shopping-bag"></i>
          <div>
            <strong>Order History</strong>
            <br><small class="text-muted">{{ user.orders.count }} orders placed</small>
          </div>
        </a>
        
        <a href="/notifications/" class="action-btn">
          <i class="fas fa-bell"></i>
          <div>
            <strong>Notifications</strong>
            <br><small class="text-muted">{{ unread_notifications }} unread</small>
          </div>
        </a>
        
        <a href="/recommendations/" class="action-btn">
          <i class="fas fa-robot"></i>
          <div>
            <strong>AI Recommendations</strong>
            <br><small class="text-muted">Personalized for you</small>
          </div>
        </a>
        
        <a href="/compare/" class="action-btn">
          <i class="fas fa-balance-scale"></i>
          <div>
            <strong>Compare Products</strong>
            <br><small class="text-muted">Make informed decisions</small>
          </div>
        </a>
      </div>

      <!-- Shopping Preferences -->
      <div class="card">
        <div class="card-header">
          <h6 class="fw-bold mb-0">
            <i class="fas fa-sliders-h me-2"></i>Shopping Preferences
          </h6>
        </div>
        <div class="card-body">
          {% if user.profile.preferred_categories.all %}
            <div class="mb-3">
              <strong>Favorite Categories:</strong>
              <div class="mt-2">
                {% for category in user.profile.preferred_categories.all %}
                  <span class="badge bg-light text-dark me-1 mb-1">{{ category.name }}</span>
                {% endfor %}
              </div>
            </div>
          {% endif %}
          
          {% if user.profile.budget_range %}
            <div class="mb-3">
              <strong>Budget Range:</strong>
              <span class="text-muted">${{ user.profile.budget_range }}</span>
            </div>
          {% endif %}
          
          <div class="mb-0">
            <strong>Notifications:</strong>
            <div class="mt-2">
              {% if user.profile.email_notifications %}
                <span class="badge bg-success me-1">Email</span>
              {% endif %}
              {% if user.profile.push_notifications %}
                <span class="badge bg-info me-1">Push</span>
              {% endif %}
              {% if not user.profile.email_notifications and not user.profile.push_notifications %}
                <span class="text-muted">None enabled</span>
              {% endif %}
            </div>
          </div>
          
          <div class="mt-3">
            <a href="/profile/edit/" class="btn btn-outline-primary btn-sm w-100">
              <i class="fas fa-edit me-1"></i>Update Preferences
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Profile completion calculation
document.addEventListener('DOMContentLoaded', function() {
  // This would be calculated on the backend, but here's the logic
  let completion = 0;
  const fields = [
    '{{ user.first_name }}',
    '{{ user.last_name }}',
    '{{ user.email }}',
    '{{ user.profile.avatar }}',
    '{{ user.profile.location }}',
    '{{ user.profile.phone }}',
    '{{ user.profile.bio }}'
  ];
  
  fields.forEach(field => {
    if (field && field.trim() && field !== 'None') {
      completion += 100 / fields.length;
    }
  });
  
  // Update completion bar if needed
  const completionFill = document.querySelector('.completion-fill');
  if (completionFill) {
    setTimeout(() => {
      completionFill.style.width = Math.round(completion) + '%';
    }, 500);
  }
});

// Smooth scroll for quick actions
document.querySelectorAll('.action-btn').forEach(btn => {
  btn.addEventListener('click', function(e) {
    // Add click animation
    this.style.transform = 'scale(0.95)';
    setTimeout(() => {
      this.style.transform = '';
    }, 150);
  });
});

// Activity refresh
function refreshActivity() {
  console.log('Refreshing activity...');
  // This would make an AJAX call to refresh the activity feed
  showToast('Activity refreshed!', 'success');
}

function showToast(message, type) {
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} position-fixed`;
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
  toast.innerHTML = `
    ${message}
    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
  `;
  document.body.appendChild(toast);
  
  setTimeout(() => {
    if (toast.parentElement) {
      toast.remove();
    }
  }, 3000);
}
</script>
{% endblock %}
