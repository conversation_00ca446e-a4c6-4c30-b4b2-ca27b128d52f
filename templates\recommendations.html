{% extends 'base.html' %}
{% load static %}

{% block title %}AI Recommendations - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="Get personalized product recommendations powered by AI. Discover products tailored to your preferences and shopping history.">
<style>
  .recommendations-hero {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
  }
  .ai-badge {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    color: white;
    border-radius: 2rem;
    padding: 0.5rem 1rem;
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    margin-bottom: 1rem;
  }
  .recommendation-card {
    border: none;
    border-radius: 1rem;
    transition: all 0.3s ease;
    height: 100%;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
  }
  .recommendation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.15);
  }
  .recommendation-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
  }
  .product-image {
    height: 200px;
    object-fit: cover;
    border-radius: 0 0 1rem 1rem;
  }
  .confidence-score {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(0,0,0,0.8);
    color: white;
    border-radius: 2rem;
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
    font-weight: bold;
  }
  .recommendation-reason {
    background: #e3f0ff;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin-top: 1rem;
    font-size: 0.9rem;
  }
  .category-section {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
  }
  .filter-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }
  .filter-chip {
    background: #e3f0ff;
    color: #0d6efd;
    border: 1px solid #0d6efd;
    border-radius: 2rem;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  .filter-chip.active {
    background: #0d6efd;
    color: white;
  }
  .filter-chip:hover {
    background: #0d6efd;
    color: white;
  }
  .ai-insights {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
  }
  .insight-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
  }
  .insight-icon {
    width: 40px;
    height: 40px;
    background: #ffc107;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: #000;
  }
  .trending-badge {
    background: #dc3545;
    color: white;
    border-radius: 2rem;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: bold;
    position: absolute;
    top: 1rem;
    left: 1rem;
  }
  .personalization-panel {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 100px;
  }
  .preference-slider {
    margin-bottom: 1.5rem;
  }
  .loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 0.5rem;
  }
  @keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="recommendations-hero">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-8">
        <div class="ai-badge">
          <i class="fas fa-robot me-2"></i>AI Powered
        </div>
        <h1 class="display-4 fw-bold mb-4">Personalized Recommendations</h1>
        <p class="lead mb-4">Discover products tailored just for you using advanced AI algorithms that learn from your preferences and shopping behavior.</p>
        <div class="d-flex gap-3">
          <button class="btn btn-light btn-lg" onclick="refreshRecommendations()">
            <i class="fas fa-sync-alt me-2"></i>Refresh Recommendations
          </button>
          <button class="btn btn-outline-light btn-lg" onclick="customizePreferences()">
            <i class="fas fa-cog me-2"></i>Customize Preferences
          </button>
        </div>
      </div>
      <div class="col-lg-4 text-center">
        <i class="fas fa-brain" style="font-size: 6rem; opacity: 0.3;"></i>
      </div>
    </div>
  </div>
</div>

<div class="container">
  <!-- AI Insights -->
  <div class="ai-insights">
    <h3 class="fw-bold mb-4">
      <i class="fas fa-lightbulb me-2"></i>AI Insights About Your Shopping
    </h3>
    <div class="row">
      <div class="col-md-6">
        <div class="insight-item">
          <div class="insight-icon">
            <i class="fas fa-heart"></i>
          </div>
          <div>
            <h6 class="mb-1">Your Favorite Category</h6>
            <p class="text-muted mb-0">Electronics • 65% of your purchases</p>
          </div>
        </div>
        
        <div class="insight-item">
          <div class="insight-icon">
            <i class="fas fa-dollar-sign"></i>
          </div>
          <div>
            <h6 class="mb-1">Average Spending</h6>
            <p class="text-muted mb-0">$150 per purchase • Budget-conscious shopper</p>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="insight-item">
          <div class="insight-icon">
            <i class="fas fa-star"></i>
          </div>
          <div>
            <h6 class="mb-1">Quality Preference</h6>
            <p class="text-muted mb-0">You prefer highly-rated products (4+ stars)</p>
          </div>
        </div>
        
        <div class="insight-item">
          <div class="insight-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div>
            <h6 class="mb-1">Shopping Pattern</h6>
            <p class="text-muted mb-0">Most active on weekends • Evening shopper</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Personalization Panel -->
    <div class="col-lg-3">
      <div class="personalization-panel">
        <h5 class="fw-bold mb-3">
          <i class="fas fa-sliders-h me-2"></i>Customize Recommendations
        </h5>
        
        <div class="preference-slider">
          <label class="form-label">Price Range</label>
          <div class="d-flex justify-content-between text-muted small mb-2">
            <span>$0</span>
            <span>$1000+</span>
          </div>
          <input type="range" class="form-range" min="0" max="1000" value="500" id="priceRange">
          <div class="text-center mt-1">
            <small class="text-muted">Up to $<span id="priceValue">500</span></small>
          </div>
        </div>
        
        <div class="preference-slider">
          <label class="form-label">Minimum Rating</label>
          <select class="form-select" id="minRating">
            <option value="0">Any Rating</option>
            <option value="3">3+ Stars</option>
            <option value="4" selected>4+ Stars</option>
            <option value="4.5">4.5+ Stars</option>
          </select>
        </div>
        
        <div class="preference-slider">
          <label class="form-label">Preferred Categories</label>
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="electronics" checked>
            <label class="form-check-label" for="electronics">Electronics</label>
          </div>
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="fashion">
            <label class="form-check-label" for="fashion">Fashion</label>
          </div>
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="home">
            <label class="form-check-label" for="home">Home & Garden</label>
          </div>
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="sports">
            <label class="form-check-label" for="sports">Sports</label>
          </div>
        </div>
        
        <button class="btn btn-primary w-100" onclick="applyPreferences()">
          <i class="fas fa-check me-2"></i>Apply Preferences
        </button>
      </div>
    </div>

    <!-- Recommendations Content -->
    <div class="col-lg-9">
      <!-- Filter Chips -->
      <div class="filter-chips">
        <div class="filter-chip active" onclick="filterRecommendations('all')">All Recommendations</div>
        <div class="filter-chip" onclick="filterRecommendations('trending')">Trending Now</div>
        <div class="filter-chip" onclick="filterRecommendations('similar')">Similar to Your Purchases</div>
        <div class="filter-chip" onclick="filterRecommendations('deals')">Best Deals</div>
        <div class="filter-chip" onclick="filterRecommendations('new')">New Arrivals</div>
      </div>

      <!-- For You Section -->
      <div class="category-section">
        <h3 class="fw-bold mb-4">
          <i class="fas fa-user me-2"></i>Recommended For You
          <small class="text-muted">(95% match)</small>
        </h3>
        
        <div class="row">
          {% for product in personalized_recommendations %}
          <div class="col-lg-4 col-md-6 mb-4">
            <div class="card recommendation-card">
              <div class="position-relative">
                {% if product.is_trending %}
                  <span class="trending-badge">🔥 Trending</span>
                {% endif %}
                <span class="confidence-score">{{ product.confidence_score }}% match</span>
                
                {% if product.image_url %}
                  <img src="{{ product.image_url }}" class="card-img-top product-image" alt="{{ product.name }}">
                {% else %}
                  <div class="product-image bg-light d-flex align-items-center justify-content-center">
                    <i class="fas fa-image fa-3x text-muted"></i>
                  </div>
                {% endif %}
              </div>
              
              <div class="card-body">
                <h6 class="card-title">
                  <a href="/products/{{ product.id }}/" class="text-decoration-none text-dark">
                    {{ product.name|truncatechars:50 }}
                  </a>
                </h6>
                
                <div class="d-flex align-items-center mb-2">
                  <div class="text-warning me-2">
                    {% for i in "12345" %}
                      {% if forloop.counter <= product.rating %}★{% else %}☆{% endif %}
                    {% endfor %}
                  </div>
                  <small class="text-muted">({{ product.review_count }} reviews)</small>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <span class="h6 text-primary fw-bold">${{ product.price }}</span>
                  {% if product.original_price and product.original_price != product.price %}
                    <small class="text-muted text-decoration-line-through">${{ product.original_price }}</small>
                  {% endif %}
                </div>
                
                <div class="recommendation-reason">
                  <i class="fas fa-info-circle me-1"></i>
                  <strong>Why recommended:</strong> {{ product.recommendation_reason }}
                </div>
              </div>
              
              <div class="card-footer bg-transparent">
                <div class="d-flex gap-2">
                  <a href="/products/{{ product.id }}/" class="btn btn-primary btn-sm flex-fill">
                    <i class="fas fa-eye me-1"></i>View Details
                  </a>
                  <button class="btn btn-outline-primary btn-sm" onclick="addToWishlist({{ product.id }})">
                    <i class="fas fa-heart"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
          {% empty %}
          <div class="col-12">
            <div class="text-center py-4">
              <div class="loading-skeleton" style="height: 200px; margin-bottom: 1rem;"></div>
              <p class="text-muted">Loading personalized recommendations...</p>
            </div>
          </div>
          {% endfor %}
        </div>
      </div>

      <!-- Trending Products -->
      <div class="category-section">
        <h3 class="fw-bold mb-4">
          <i class="fas fa-fire me-2"></i>Trending Now
        </h3>
        
        <div class="row">
          <div class="col-lg-4 col-md-6 mb-4">
            <div class="card recommendation-card">
              <div class="position-relative">
                <span class="trending-badge">🔥 #1 Trending</span>
                <span class="confidence-score">89% match</span>
                
                <div class="product-image bg-primary d-flex align-items-center justify-content-center">
                  <i class="fas fa-mobile-alt fa-4x text-white"></i>
                </div>
              </div>
              
              <div class="card-body">
                <h6 class="card-title">Latest Smartphone Pro</h6>
                
                <div class="d-flex align-items-center mb-2">
                  <div class="text-warning me-2">★★★★★</div>
                  <small class="text-muted">(1,234 reviews)</small>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <span class="h6 text-primary fw-bold">$899</span>
                  <small class="text-muted text-decoration-line-through">$999</small>
                </div>
                
                <div class="recommendation-reason">
                  <i class="fas fa-info-circle me-1"></i>
                  <strong>Why trending:</strong> Most viewed product this week
                </div>
              </div>
              
              <div class="card-footer bg-transparent">
                <div class="d-flex gap-2">
                  <button class="btn btn-primary btn-sm flex-fill">
                    <i class="fas fa-eye me-1"></i>View Details
                  </button>
                  <button class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-heart"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- More trending products would be loaded here -->
        </div>
      </div>

      <!-- Similar to Your Purchases -->
      <div class="category-section">
        <h3 class="fw-bold mb-4">
          <i class="fas fa-history me-2"></i>Similar to Your Purchases
        </h3>
        
        <div class="row">
          <div class="col-12">
            <div class="text-center py-4">
              <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
              <h5>No Purchase History</h5>
              <p class="text-muted">Start shopping to get personalized recommendations based on your purchases!</p>
              <a href="/products/" class="btn btn-primary">Browse Products</a>
            </div>
          </div>
        </div>
      </div>

      <!-- Load More -->
      <div class="text-center mt-4">
        <button class="btn btn-outline-primary btn-lg" onclick="loadMoreRecommendations()">
          <i class="fas fa-plus me-2"></i>Load More Recommendations
        </button>
      </div>
    </div>
  </div>
</div>

<script>
// Price range slider
document.getElementById('priceRange').addEventListener('input', function() {
  document.getElementById('priceValue').textContent = this.value;
});

function filterRecommendations(type) {
  // Remove active class from all chips
  document.querySelectorAll('.filter-chip').forEach(chip => {
    chip.classList.remove('active');
  });
  
  // Add active class to clicked chip
  event.target.classList.add('active');
  
  console.log('Filtering recommendations by:', type);
  // Implement filtering logic here
}

function refreshRecommendations() {
  console.log('Refreshing recommendations...');
  showToast('Refreshing recommendations...', 'info');
  
  // Simulate loading
  setTimeout(() => {
    showToast('Recommendations updated!', 'success');
  }, 2000);
}

function customizePreferences() {
  // Scroll to personalization panel
  document.querySelector('.personalization-panel').scrollIntoView({
    behavior: 'smooth'
  });
}

function applyPreferences() {
  const priceRange = document.getElementById('priceRange').value;
  const minRating = document.getElementById('minRating').value;
  const categories = [];
  
  document.querySelectorAll('.personalization-panel input[type="checkbox"]:checked').forEach(checkbox => {
    categories.push(checkbox.id);
  });
  
  console.log('Applying preferences:', { priceRange, minRating, categories });
  showToast('Preferences applied! Updating recommendations...', 'success');
  
  // Simulate recommendation update
  setTimeout(() => {
    refreshRecommendations();
  }, 1000);
}

function addToWishlist(productId) {
  console.log('Adding to wishlist:', productId);
  showToast('Added to wishlist!', 'success');
}

function loadMoreRecommendations() {
  console.log('Loading more recommendations...');
  showToast('Loading more recommendations...', 'info');
}

function showToast(message, type) {
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} position-fixed`;
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
  toast.innerHTML = `
    ${message}
    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
  `;
  document.body.appendChild(toast);
  
  setTimeout(() => {
    if (toast.parentElement) {
      toast.remove();
    }
  }, 3000);
}

// Simulate loading recommendations on page load
document.addEventListener('DOMContentLoaded', function() {
  console.log('Loading AI recommendations...');
  
  // Simulate API call delay
  setTimeout(() => {
    // Remove loading skeletons and show actual content
    document.querySelectorAll('.loading-skeleton').forEach(skeleton => {
      skeleton.style.display = 'none';
    });
  }, 2000);
});
</script>
{% endblock %}
