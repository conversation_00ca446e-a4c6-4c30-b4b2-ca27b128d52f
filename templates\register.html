{% extends 'base.html' %}
{% load static %}

{% block title %}Create Account - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="Create your Best in Click account to access personalized recommendations, exclusive deals, and smart shopping features.">
<style>
  .register-container {
    min-height: 80vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 2rem 0;
  }
  .register-card {
    background: white;
    border-radius: 1.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
    max-width: 1000px;
    margin: 0 auto;
  }
  .register-left {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    color: white;
    padding: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
  }
  .register-right {
    padding: 3rem;
  }
  .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }
  .password-strength {
    margin-top: 0.5rem;
  }
  .strength-bar {
    height: 4px;
    border-radius: 2px;
    background: #e9ecef;
    overflow: hidden;
  }
  .strength-fill {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
  }
  .strength-weak { background: #dc3545; width: 25%; }
  .strength-fair { background: #ffc107; width: 50%; }
  .strength-good { background: #28a745; width: 75%; }
  .strength-strong { background: #20c997; width: 100%; }
  .password-requirements {
    font-size: 0.875rem;
    margin-top: 0.5rem;
  }
  .requirement {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
  }
  .requirement.met {
    color: #28a745;
  }
  .requirement.unmet {
    color: #dc3545;
  }
  .social-register {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 0.75rem;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
  }
  .social-register:hover {
    background: #f8f9fa;
    text-decoration: none;
  }
  .social-register.google { color: #db4437; }
  .social-register.facebook { color: #4267B2; }
  .social-register.apple { color: #000; }
  .divider {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
  }
  .divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #dee2e6;
  }
  .divider span {
    background: white;
    padding: 0 1rem;
    color: #6c757d;
  }
  .benefits-list {
    list-style: none;
    padding: 0;
  }
  .benefits-list li {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background: rgba(255,255,255,0.1);
    border-radius: 0.5rem;
  }
  .benefits-list i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
  }
  .form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
  }
  .step-indicator {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
  }
  .step {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 0.5rem;
    font-weight: bold;
    position: relative;
  }
  .step.active {
    background: #0d6efd;
    color: white;
  }
  .step.completed {
    background: #28a745;
    color: white;
  }
  .step::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 100%;
    width: 30px;
    height: 2px;
    background: #e9ecef;
    transform: translateY(-50%);
  }
  .step:last-child::after {
    display: none;
  }
</style>
{% endblock %}

{% block content %}
<div class="register-container">
  <div class="container">
    <div class="register-card">
      <div class="row g-0">
        <!-- Left Side - Benefits -->
        <div class="col-lg-5 d-none d-lg-block">
          <div class="register-left">
            <div>
              <i class="fas fa-rocket fa-4x mb-4" style="opacity: 0.8;"></i>
              <h2 class="fw-bold mb-3">Join Best in Click</h2>
              <p class="lead mb-4">Create your account and unlock the power of AI-driven smart shopping.</p>
              
              <ul class="benefits-list">
                <li>
                  <i class="fas fa-robot"></i>
                  <div>
                    <strong>AI Recommendations</strong><br>
                    <small>Personalized product suggestions</small>
                  </div>
                </li>
                <li>
                  <i class="fas fa-heart"></i>
                  <div>
                    <strong>Wishlist & Favorites</strong><br>
                    <small>Save and organize products you love</small>
                  </div>
                </li>
                <li>
                  <i class="fas fa-bell"></i>
                  <div>
                    <strong>Price Alerts</strong><br>
                    <small>Get notified when prices drop</small>
                  </div>
                </li>
                <li>
                  <i class="fas fa-balance-scale"></i>
                  <div>
                    <strong>Product Comparison</strong><br>
                    <small>Compare products side by side</small>
                  </div>
                </li>
                <li>
                  <i class="fas fa-tags"></i>
                  <div>
                    <strong>Exclusive Deals</strong><br>
                    <small>Access member-only discounts</small>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
        
        <!-- Right Side - Registration Form -->
        <div class="col-lg-7">
          <div class="register-right">
            <div class="text-center mb-4">
              <h3 class="fw-bold">Create Your Account</h3>
              <p class="text-muted">Join thousands of smart shoppers today</p>
            </div>

            <!-- Step Indicator -->
            <div class="step-indicator">
              <div class="step active" id="step1">1</div>
              <div class="step" id="step2">2</div>
              <div class="step" id="step3">3</div>
            </div>

            <!-- Error Messages -->
            {% if messages %}
              {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                  {{ message }}
                  <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
              {% endfor %}
            {% endif %}

            <!-- Social Registration -->
            <div class="mb-4">
              <a href="#" class="social-register google" onclick="socialRegister('google')">
                <i class="fab fa-google me-2"></i>
                Sign up with Google
              </a>
              
              <a href="#" class="social-register facebook" onclick="socialRegister('facebook')">
                <i class="fab fa-facebook-f me-2"></i>
                Sign up with Facebook
              </a>
              
              <a href="#" class="social-register apple" onclick="socialRegister('apple')">
                <i class="fab fa-apple me-2"></i>
                Sign up with Apple
              </a>
            </div>

            <div class="divider">
              <span>or create account with email</span>
            </div>

            <!-- Registration Form -->
            <form method="post" id="registerForm">
              {% csrf_token %}
              
              <!-- Step 1: Basic Information -->
              <div class="form-step" id="formStep1">
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="firstName" class="form-label">First Name</label>
                    <input type="text" class="form-control" id="firstName" name="first_name" 
                           placeholder="Enter your first name" required>
                    <div class="invalid-feedback">
                      Please enter your first name.
                    </div>
                  </div>
                  
                  <div class="col-md-6 mb-3">
                    <label for="lastName" class="form-label">Last Name</label>
                    <input type="text" class="form-control" id="lastName" name="last_name" 
                           placeholder="Enter your last name" required>
                    <div class="invalid-feedback">
                      Please enter your last name.
                    </div>
                  </div>
                </div>
                
                <div class="mb-3">
                  <label for="email" class="form-label">Email Address</label>
                  <input type="email" class="form-control" id="email" name="email" 
                         placeholder="Enter your email address" required>
                  <div class="invalid-feedback">
                    Please enter a valid email address.
                  </div>
                </div>
                
                <div class="mb-3">
                  <label for="phone" class="form-label">Phone Number (Optional)</label>
                  <input type="tel" class="form-control" id="phone" name="phone" 
                         placeholder="Enter your phone number">
                </div>
                
                <button type="button" class="btn btn-primary w-100" onclick="nextStep(2)">
                  Continue <i class="fas fa-arrow-right ms-2"></i>
                </button>
              </div>

              <!-- Step 2: Password & Security -->
              <div class="form-step" id="formStep2" style="display: none;">
                <div class="mb-3">
                  <label for="password" class="form-label">Password</label>
                  <input type="password" class="form-control" id="password" name="password" 
                         placeholder="Create a strong password" required onkeyup="checkPasswordStrength()">
                  
                  <div class="password-strength">
                    <div class="strength-bar">
                      <div class="strength-fill" id="strengthFill"></div>
                    </div>
                    <small class="text-muted" id="strengthText">Password strength</small>
                  </div>
                  
                  <div class="password-requirements">
                    <div class="requirement unmet" id="req-length">
                      <i class="fas fa-times me-2"></i>At least 8 characters
                    </div>
                    <div class="requirement unmet" id="req-uppercase">
                      <i class="fas fa-times me-2"></i>One uppercase letter
                    </div>
                    <div class="requirement unmet" id="req-lowercase">
                      <i class="fas fa-times me-2"></i>One lowercase letter
                    </div>
                    <div class="requirement unmet" id="req-number">
                      <i class="fas fa-times me-2"></i>One number
                    </div>
                  </div>
                </div>
                
                <div class="mb-3">
                  <label for="confirmPassword" class="form-label">Confirm Password</label>
                  <input type="password" class="form-control" id="confirmPassword" name="confirm_password" 
                         placeholder="Confirm your password" required onkeyup="checkPasswordMatch()">
                  <div class="invalid-feedback">
                    Passwords do not match.
                  </div>
                </div>
                
                <div class="d-flex gap-2">
                  <button type="button" class="btn btn-outline-secondary" onclick="prevStep(1)">
                    <i class="fas fa-arrow-left me-2"></i>Back
                  </button>
                  <button type="button" class="btn btn-primary flex-fill" onclick="nextStep(3)">
                    Continue <i class="fas fa-arrow-right ms-2"></i>
                  </button>
                </div>
              </div>

              <!-- Step 3: Preferences & Terms -->
              <div class="form-step" id="formStep3" style="display: none;">
                <div class="mb-3">
                  <label class="form-label">Shopping Preferences (Optional)</label>
                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="electronics" name="interests" value="electronics">
                        <label class="form-check-label" for="electronics">Electronics</label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="fashion" name="interests" value="fashion">
                        <label class="form-check-label" for="fashion">Fashion</label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="home" name="interests" value="home">
                        <label class="form-check-label" for="home">Home & Garden</label>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="sports" name="interests" value="sports">
                        <label class="form-check-label" for="sports">Sports & Fitness</label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="books" name="interests" value="books">
                        <label class="form-check-label" for="books">Books</label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="gaming" name="interests" value="gaming">
                        <label class="form-check-label" for="gaming">Gaming</label>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="mb-3">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="newsletter" name="newsletter">
                    <label class="form-check-label" for="newsletter">
                      Subscribe to our newsletter for deals and updates
                    </label>
                  </div>
                </div>
                
                <div class="mb-3">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                    <label class="form-check-label" for="terms">
                      I agree to the <a href="/terms/" target="_blank">Terms of Service</a> and 
                      <a href="/privacy/" target="_blank">Privacy Policy</a>
                    </label>
                  </div>
                </div>
                
                <div class="d-flex gap-2">
                  <button type="button" class="btn btn-outline-secondary" onclick="prevStep(2)">
                    <i class="fas fa-arrow-left me-2"></i>Back
                  </button>
                  <button type="submit" class="btn btn-primary flex-fill">
                    <i class="fas fa-user-plus me-2"></i>Create Account
                  </button>
                </div>
              </div>
            </form>

            <!-- Sign In Link -->
            <div class="text-center mt-4">
              <p class="text-muted">
                Already have an account? 
                <a href="/login/" class="text-decoration-none fw-semibold">Sign in here</a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
let currentStep = 1;

function nextStep(step) {
  if (validateCurrentStep()) {
    // Hide current step
    document.getElementById(`formStep${currentStep}`).style.display = 'none';
    document.getElementById(`step${currentStep}`).classList.remove('active');
    document.getElementById(`step${currentStep}`).classList.add('completed');
    
    // Show next step
    currentStep = step;
    document.getElementById(`formStep${currentStep}`).style.display = 'block';
    document.getElementById(`step${currentStep}`).classList.add('active');
  }
}

function prevStep(step) {
  // Hide current step
  document.getElementById(`formStep${currentStep}`).style.display = 'none';
  document.getElementById(`step${currentStep}`).classList.remove('active');
  
  // Show previous step
  currentStep = step;
  document.getElementById(`formStep${currentStep}`).style.display = 'block';
  document.getElementById(`step${currentStep}`).classList.remove('completed');
  document.getElementById(`step${currentStep}`).classList.add('active');
}

function validateCurrentStep() {
  const currentStepElement = document.getElementById(`formStep${currentStep}`);
  const inputs = currentStepElement.querySelectorAll('input[required]');
  let isValid = true;
  
  inputs.forEach(input => {
    input.classList.remove('is-invalid');
    
    if (!input.value.trim()) {
      input.classList.add('is-invalid');
      isValid = false;
    } else if (input.type === 'email' && !isValidEmail(input.value)) {
      input.classList.add('is-invalid');
      isValid = false;
    }
  });
  
  if (currentStep === 2) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    if (password !== confirmPassword) {
      document.getElementById('confirmPassword').classList.add('is-invalid');
      isValid = false;
    }
    
    if (!isStrongPassword(password)) {
      document.getElementById('password').classList.add('is-invalid');
      isValid = false;
    }
  }
  
  return isValid;
}

function checkPasswordStrength() {
  const password = document.getElementById('password').value;
  const strengthFill = document.getElementById('strengthFill');
  const strengthText = document.getElementById('strengthText');
  
  let score = 0;
  let strength = '';
  
  // Length check
  if (password.length >= 8) {
    score++;
    updateRequirement('req-length', true);
  } else {
    updateRequirement('req-length', false);
  }
  
  // Uppercase check
  if (/[A-Z]/.test(password)) {
    score++;
    updateRequirement('req-uppercase', true);
  } else {
    updateRequirement('req-uppercase', false);
  }
  
  // Lowercase check
  if (/[a-z]/.test(password)) {
    score++;
    updateRequirement('req-lowercase', true);
  } else {
    updateRequirement('req-lowercase', false);
  }
  
  // Number check
  if (/\d/.test(password)) {
    score++;
    updateRequirement('req-number', true);
  } else {
    updateRequirement('req-number', false);
  }
  
  // Update strength indicator
  strengthFill.className = 'strength-fill';
  switch (score) {
    case 0:
    case 1:
      strengthFill.classList.add('strength-weak');
      strength = 'Weak';
      break;
    case 2:
      strengthFill.classList.add('strength-fair');
      strength = 'Fair';
      break;
    case 3:
      strengthFill.classList.add('strength-good');
      strength = 'Good';
      break;
    case 4:
      strengthFill.classList.add('strength-strong');
      strength = 'Strong';
      break;
  }
  
  strengthText.textContent = `Password strength: ${strength}`;
}

function updateRequirement(id, met) {
  const element = document.getElementById(id);
  const icon = element.querySelector('i');
  
  if (met) {
    element.classList.remove('unmet');
    element.classList.add('met');
    icon.classList.remove('fa-times');
    icon.classList.add('fa-check');
  } else {
    element.classList.remove('met');
    element.classList.add('unmet');
    icon.classList.remove('fa-check');
    icon.classList.add('fa-times');
  }
}

function checkPasswordMatch() {
  const password = document.getElementById('password').value;
  const confirmPassword = document.getElementById('confirmPassword').value;
  const confirmInput = document.getElementById('confirmPassword');
  
  if (password && confirmPassword) {
    if (password === confirmPassword) {
      confirmInput.classList.remove('is-invalid');
      confirmInput.classList.add('is-valid');
    } else {
      confirmInput.classList.remove('is-valid');
      confirmInput.classList.add('is-invalid');
    }
  }
}

function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function isStrongPassword(password) {
  return password.length >= 8 && 
         /[A-Z]/.test(password) && 
         /[a-z]/.test(password) && 
         /\d/.test(password);
}

function socialRegister(provider) {
  console.log('Social registration with:', provider);
  showToast(`Redirecting to ${provider}...`, 'info');
  
  setTimeout(() => {
    showToast(`${provider} registration would be implemented here`, 'warning');
  }, 1000);
}

function showToast(message, type) {
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} position-fixed`;
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
  toast.innerHTML = `
    ${message}
    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
  `;
  document.body.appendChild(toast);
  
  setTimeout(() => {
    if (toast.parentElement) {
      toast.remove();
    }
  }, 3000);
}

// Form submission
document.getElementById('registerForm').addEventListener('submit', function(e) {
  if (!validateCurrentStep()) {
    e.preventDefault();
    showToast('Please fix the errors in the form', 'danger');
    return;
  }
  
  // Show loading state
  const submitBtn = this.querySelector('button[type="submit"]');
  const originalText = submitBtn.innerHTML;
  submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating Account...';
  submitBtn.disabled = true;
  
  // Re-enable button after 3 seconds (in case of error)
  setTimeout(() => {
    submitBtn.innerHTML = originalText;
    submitBtn.disabled = false;
  }, 3000);
});
</script>
{% endblock %}
