{% extends 'base.html' %}

{% block title %}My Reviews - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="View and manage your product reviews on Best in Click. Share your shopping experiences with other customers.">
<style>
  .reviews-header {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #000;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
  }
  .reviews-header::before {
    content: '★';
    position: absolute;
    top: -20px;
    right: -20px;
    font-size: 8rem;
    opacity: 0.1;
    transform: rotate(15deg);
  }
  .reviews-stats {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
  }
  .stat-item {
    text-align: center;
    padding: 1rem;
  }
  .stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #ffc107;
    display: block;
  }
  .stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.5rem;
  }
  .review-card {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
  }
  .review-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.15);
  }
  .review-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ffc107, #fd7e14);
  }
  .product-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 0.5rem;
  }
  .rating-stars {
    color: #ffc107;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
  }
  .review-actions {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  .review-card:hover .review-actions {
    opacity: 1;
  }
  .action-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  .action-btn.edit {
    background: #17a2b8;
  }
  .action-btn.delete {
    background: #dc3545;
  }
  .action-btn.share {
    background: #6f42c1;
  }
  .action-btn:hover {
    transform: scale(1.1);
  }
  .empty-reviews {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }
  .filter-bar {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  .review-status {
    padding: 0.25rem 0.75rem;
    border-radius: 2rem;
    font-size: 0.8rem;
    font-weight: bold;
  }
  .status-published {
    background: #d4edda;
    color: #155724;
  }
  .status-pending {
    background: #fff3cd;
    color: #856404;
  }
  .status-rejected {
    background: #f8d7da;
    color: #721c24;
  }
  .helpful-votes {
    background: #e3f0ff;
    color: #0d6efd;
    border-radius: 2rem;
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
    font-weight: bold;
    display: inline-flex;
    align-items: center;
  }
  .helpful-votes i {
    margin-right: 0.25rem;
  }
  .review-metrics {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  .metric-item {
    text-align: center;
    padding: 1rem;
  }
  .metric-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #0d6efd;
  }
  .metric-label {
    color: #6c757d;
    font-size: 0.9rem;
  }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
  <!-- Reviews Header -->
  <div class="reviews-header">
    <div class="row align-items-center">
      <div class="col-md-8">
        <h1 class="fw-bold mb-2">
          <i class="fas fa-star me-3"></i>My Reviews
        </h1>
        <p class="mb-0">Share your shopping experiences and help other customers make informed decisions</p>
      </div>
      <div class="col-md-4 text-end">
        <a href="/reviews/write/" class="btn btn-dark btn-lg">
          <i class="fas fa-plus me-2"></i>Write Review
        </a>
      </div>
    </div>
  </div>

  <!-- Reviews Statistics -->
  <div class="reviews-stats">
    <div class="row">
      <div class="col-md-3">
        <div class="stat-item">
          <span class="stat-number">{{ user.reviews.count|default:0 }}</span>
          <div class="stat-label">Total Reviews</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stat-item">
          <span class="stat-number">{{ average_rating|floatformat:1|default:"0.0" }}</span>
          <div class="stat-label">Average Rating</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stat-item">
          <span class="stat-number">{{ helpful_votes|default:0 }}</span>
          <div class="stat-label">Helpful Votes</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stat-item">
          <span class="stat-number">{{ reviewer_rank|default:"Beginner" }}</span>
          <div class="stat-label">Reviewer Rank</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Review Metrics -->
  <div class="review-metrics">
    <h5 class="fw-bold mb-3">
      <i class="fas fa-chart-bar me-2"></i>Review Impact
    </h5>
    <div class="row">
      <div class="col-md-4">
        <div class="metric-item">
          <div class="metric-value">{{ total_views|default:0 }}</div>
          <div class="metric-label">Total Views</div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="metric-item">
          <div class="metric-value">{{ this_month_reviews|default:0 }}</div>
          <div class="metric-label">This Month</div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="metric-item">
          <div class="metric-value">{{ response_rate|default:0 }}%</div>
          <div class="metric-label">Response Rate</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filter and Sort Options -->
  <div class="filter-bar">
    <div class="row align-items-center">
      <div class="col-md-3">
        <select class="form-select" id="statusFilter" onchange="filterReviews()">
          <option value="">All Reviews</option>
          <option value="published">Published</option>
          <option value="pending">Pending</option>
          <option value="rejected">Rejected</option>
        </select>
      </div>
      
      <div class="col-md-3">
        <select class="form-select" id="ratingFilter" onchange="filterReviews()">
          <option value="">All Ratings</option>
          <option value="5">5 Stars</option>
          <option value="4">4 Stars</option>
          <option value="3">3 Stars</option>
          <option value="2">2 Stars</option>
          <option value="1">1 Star</option>
        </select>
      </div>
      
      <div class="col-md-3">
        <select class="form-select" id="sortBy" onchange="sortReviews()">
          <option value="date_desc">Newest First</option>
          <option value="date_asc">Oldest First</option>
          <option value="rating_desc">Highest Rated</option>
          <option value="rating_asc">Lowest Rated</option>
          <option value="helpful">Most Helpful</option>
        </select>
      </div>
      
      <div class="col-md-3">
        <input type="text" class="form-control" placeholder="Search reviews..." id="searchReviews" onkeyup="searchReviews()">
      </div>
    </div>
  </div>

  <!-- Reviews List -->
  {% if user_reviews %}
    <div id="reviewsList">
      {% for review in user_reviews %}
        <div class="review-card" data-status="{{ review.status }}" data-rating="{{ review.rating }}">
          <!-- Review Actions -->
          <div class="review-actions">
            <button class="action-btn edit" onclick="editReview({{ review.id }})" title="Edit review">
              <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn share" onclick="shareReview({{ review.id }})" title="Share review">
              <i class="fas fa-share-alt"></i>
            </button>
            <button class="action-btn delete" onclick="deleteReview({{ review.id }})" title="Delete review">
              <i class="fas fa-trash"></i>
            </button>
          </div>

          <div class="card-body">
            <div class="row">
              <div class="col-md-2">
                {% if review.product.image_url %}
                  <img src="{{ review.product.image_url }}" class="product-image" alt="{{ review.product.name }}">
                {% else %}
                  <div class="product-image bg-light d-flex align-items-center justify-content-center">
                    <i class="fas fa-image text-muted"></i>
                  </div>
                {% endif %}
              </div>
              
              <div class="col-md-10">
                <div class="d-flex justify-content-between align-items-start mb-2">
                  <div>
                    <h6 class="mb-1">
                      <a href="/products/{{ review.product.id }}/" class="text-decoration-none text-dark">
                        {{ review.product.name|truncatechars:60 }}
                      </a>
                    </h6>
                    <div class="rating-stars">
                      {% for i in "12345" %}
                        {% if forloop.counter <= review.rating %}★{% else %}☆{% endif %}
                      {% endfor %}
                    </div>
                  </div>
                  
                  <div class="text-end">
                    <span class="review-status status-{{ review.status }}">
                      {{ review.get_status_display }}
                    </span>
                    {% if review.helpful_votes > 0 %}
                      <div class="helpful-votes mt-1">
                        <i class="fas fa-thumbs-up"></i>{{ review.helpful_votes }}
                      </div>
                    {% endif %}
                  </div>
                </div>
                
                <p class="mb-2">{{ review.text|truncatechars:200 }}</p>
                
                <div class="d-flex justify-content-between align-items-center">
                  <small class="text-muted">
                    <i class="fas fa-calendar me-1"></i>{{ review.created_at|date:"M d, Y" }}
                    {% if review.verified_purchase %}
                      <span class="badge bg-success ms-2">Verified Purchase</span>
                    {% endif %}
                  </small>
                  
                  <div>
                    {% if review.status == 'published' %}
                      <small class="text-muted">
                        <i class="fas fa-eye me-1"></i>{{ review.views|default:0 }} views
                      </small>
                    {% endif %}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      {% endfor %}
    </div>

    <!-- Load More -->
    {% if has_more_reviews %}
      <div class="text-center mt-4">
        <button class="btn btn-outline-primary btn-lg" onclick="loadMoreReviews()">
          <i class="fas fa-plus me-2"></i>Load More Reviews
        </button>
      </div>
    {% endif %}

  {% else %}
    <!-- Empty Reviews -->
    <div class="empty-reviews">
      <i class="fas fa-star-half-alt fa-5x text-muted mb-4"></i>
      <h3>No Reviews Yet</h3>
      <p class="text-muted mb-4">Share your shopping experiences by writing your first review</p>
      
      <div class="d-flex justify-content-center gap-3 mb-4">
        <a href="/reviews/write/" class="btn btn-primary btn-lg">
          <i class="fas fa-plus me-2"></i>Write Your First Review
        </a>
        <a href="/products/" class="btn btn-outline-primary btn-lg">
          <i class="fas fa-shopping-bag me-2"></i>Browse Products
        </a>
      </div>

      <!-- Review Benefits -->
      <div class="row mt-5">
        <div class="col-md-4">
          <div class="text-center">
            <i class="fas fa-users fa-2x text-primary mb-3"></i>
            <h6>Help Others</h6>
            <p class="text-muted small">Your reviews help other customers make informed decisions</p>
          </div>
        </div>
        <div class="col-md-4">
          <div class="text-center">
            <i class="fas fa-trophy fa-2x text-success mb-3"></i>
            <h6>Earn Rewards</h6>
            <p class="text-muted small">Get points and badges for writing helpful reviews</p>
          </div>
        </div>
        <div class="col-md-4">
          <div class="text-center">
            <i class="fas fa-star fa-2x text-warning mb-3"></i>
            <h6>Build Reputation</h6>
            <p class="text-muted small">Become a trusted reviewer in the community</p>
          </div>
        </div>
      </div>
    </div>
  {% endif %}
</div>

<script>
function filterReviews() {
  const statusFilter = document.getElementById('statusFilter').value;
  const ratingFilter = document.getElementById('ratingFilter').value;
  const reviews = document.querySelectorAll('.review-card');
  
  reviews.forEach(review => {
    let showReview = true;
    
    if (statusFilter && review.dataset.status !== statusFilter) {
      showReview = false;
    }
    
    if (ratingFilter && review.dataset.rating !== ratingFilter) {
      showReview = false;
    }
    
    review.style.display = showReview ? 'block' : 'none';
  });
}

function sortReviews() {
  const sortBy = document.getElementById('sortBy').value;
  const reviewsList = document.getElementById('reviewsList');
  const reviews = Array.from(reviewsList.children);
  
  reviews.sort((a, b) => {
    switch (sortBy) {
      case 'rating_desc':
        return parseInt(b.dataset.rating) - parseInt(a.dataset.rating);
      case 'rating_asc':
        return parseInt(a.dataset.rating) - parseInt(b.dataset.rating);
      // Add more sorting options as needed
      default:
        return 0;
    }
  });
  
  reviews.forEach(review => reviewsList.appendChild(review));
}

function searchReviews() {
  const searchTerm = document.getElementById('searchReviews').value.toLowerCase();
  const reviews = document.querySelectorAll('.review-card');
  
  reviews.forEach(review => {
    const text = review.textContent.toLowerCase();
    review.style.display = text.includes(searchTerm) ? 'block' : 'none';
  });
}

function editReview(reviewId) {
  window.location.href = `/reviews/edit/${reviewId}/`;
}

function shareReview(reviewId) {
  if (navigator.share) {
    navigator.share({
      title: 'Check out my review',
      url: `/reviews/${reviewId}/`
    });
  } else {
    navigator.clipboard.writeText(`${window.location.origin}/reviews/${reviewId}/`);
    showToast('Review link copied to clipboard!', 'success');
  }
}

function deleteReview(reviewId) {
  if (confirm('Are you sure you want to delete this review? This action cannot be undone.')) {
    console.log('Deleting review:', reviewId);
    
    // Remove from DOM with animation
    const reviewCard = document.querySelector(`[onclick="deleteReview(${reviewId})"]`).closest('.review-card');
    reviewCard.style.opacity = '0';
    reviewCard.style.transform = 'scale(0.8)';
    
    setTimeout(() => {
      reviewCard.remove();
      updateReviewStats();
    }, 300);
    
    showToast('Review deleted successfully', 'success');
  }
}

function loadMoreReviews() {
  console.log('Loading more reviews...');
  showToast('Loading more reviews...', 'info');
}

function updateReviewStats() {
  // Update statistics after review deletion
  const remainingReviews = document.querySelectorAll('.review-card').length;
  document.querySelector('.stat-number').textContent = remainingReviews;
}

function showToast(message, type) {
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} position-fixed`;
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
  toast.innerHTML = `
    ${message}
    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
  `;
  document.body.appendChild(toast);
  
  setTimeout(() => {
    if (toast.parentElement) {
      toast.remove();
    }
  }, 3000);
}
</script>
{% endblock %}
