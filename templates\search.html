{% extends 'base.html' %}
{% load static %}

{% block title %}Search Results{% if query %} for "{{ query }}"{% endif %} - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="Search results for products, stores, and brands on Best in Click. Find exactly what you're looking for with our advanced search.">
<style>
  .search-hero {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
  }
  .search-suggestions {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
  }
  .suggestion-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
  }
  .suggestion-item:hover {
    background: #f8f9fa;
  }
  .suggestion-item:last-child {
    border-bottom: none;
  }
  .search-filters {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  .filter-chip {
    background: #e3f0ff;
    color: #0d6efd;
    border: 1px solid #0d6efd;
    border-radius: 2rem;
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    margin: 0.25rem;
    display: inline-flex;
    align-items: center;
  }
  .filter-chip .remove {
    margin-left: 0.5rem;
    cursor: pointer;
    font-weight: bold;
  }
  .search-result-card {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
  }
  .search-result-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.15);
  }
  .result-image {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 0.5rem;
  }
  .result-type-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
  .search-stats {
    background: #e3f0ff;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
  }
  .no-results {
    text-align: center;
    padding: 3rem 0;
  }
  .trending-searches {
    background: #fff3cd;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  .trending-tag {
    background: #ffc107;
    color: #000;
    border-radius: 1rem;
    padding: 0.25rem 0.75rem;
    text-decoration: none;
    font-size: 0.875rem;
    margin: 0.25rem;
    display: inline-block;
    transition: all 0.3s ease;
  }
  .trending-tag:hover {
    background: #ffb300;
    color: #000;
    transform: scale(1.05);
  }
  .search-autocomplete {
    position: relative;
  }
</style>
{% endblock %}

{% block content %}
<!-- Search Hero -->
<div class="search-hero">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-8">
        <h1 class="display-5 fw-bold mb-3">
          {% if query %}
            Search Results for "{{ query }}"
          {% else %}
            Advanced Search
          {% endif %}
        </h1>
        <p class="lead mb-0">Find products, stores, and brands across our platform</p>
      </div>
      <div class="col-lg-4 text-center">
        <i class="fas fa-search" style="font-size: 4rem; opacity: 0.3;"></i>
      </div>
    </div>
  </div>
</div>

<div class="container">
  <!-- Advanced Search Form -->
  <div class="search-filters">
    <form method="get" id="searchForm">
      <div class="row">
        <div class="col-lg-6 mb-3">
          <div class="search-autocomplete">
            <label for="searchQuery" class="form-label fw-semibold">Search Query</label>
            <input type="text" class="form-control form-control-lg" id="searchQuery" name="q" 
                   value="{{ query }}" placeholder="Search for products, brands, or stores..."
                   autocomplete="off" onkeyup="showSuggestions(this.value)">
            <div class="search-suggestions" id="suggestions" style="display: none;"></div>
          </div>
        </div>
        
        <div class="col-lg-3 mb-3">
          <label for="searchType" class="form-label fw-semibold">Search In</label>
          <select class="form-select" id="searchType" name="type">
            <option value="all" {% if request.GET.type == 'all' %}selected{% endif %}>All</option>
            <option value="products" {% if request.GET.type == 'products' %}selected{% endif %}>Products</option>
            <option value="stores" {% if request.GET.type == 'stores' %}selected{% endif %}>Stores</option>
            <option value="brands" {% if request.GET.type == 'brands' %}selected{% endif %}>Brands</option>
          </select>
        </div>
        
        <div class="col-lg-3 mb-3">
          <label for="sortBy" class="form-label fw-semibold">Sort By</label>
          <select class="form-select" id="sortBy" name="sort">
            <option value="relevance" {% if request.GET.sort == 'relevance' %}selected{% endif %}>Relevance</option>
            <option value="price_low" {% if request.GET.sort == 'price_low' %}selected{% endif %}>Price: Low to High</option>
            <option value="price_high" {% if request.GET.sort == 'price_high' %}selected{% endif %}>Price: High to Low</option>
            <option value="rating" {% if request.GET.sort == 'rating' %}selected{% endif %}>Highest Rated</option>
            <option value="newest" {% if request.GET.sort == 'newest' %}selected{% endif %}>Newest</option>
          </select>
        </div>
      </div>
      
      <div class="row">
        <div class="col-lg-3 mb-3">
          <label for="category" class="form-label fw-semibold">Category</label>
          <select class="form-select" id="category" name="category">
            <option value="">All Categories</option>
            {% for category in categories %}
              <option value="{{ category.id }}" {% if request.GET.category == category.id|stringformat:"s" %}selected{% endif %}>
                {{ category.name }}
              </option>
            {% endfor %}
          </select>
        </div>
        
        <div class="col-lg-3 mb-3">
          <label for="minPrice" class="form-label fw-semibold">Min Price</label>
          <input type="number" class="form-control" id="minPrice" name="min_price" 
                 value="{{ request.GET.min_price }}" placeholder="0" min="0" step="0.01">
        </div>
        
        <div class="col-lg-3 mb-3">
          <label for="maxPrice" class="form-label fw-semibold">Max Price</label>
          <input type="number" class="form-control" id="maxPrice" name="max_price" 
                 value="{{ request.GET.max_price }}" placeholder="1000" min="0" step="0.01">
        </div>
        
        <div class="col-lg-3 mb-3">
          <label for="rating" class="form-label fw-semibold">Min Rating</label>
          <select class="form-select" id="rating" name="rating">
            <option value="">Any Rating</option>
            <option value="4" {% if request.GET.rating == '4' %}selected{% endif %}>4+ Stars</option>
            <option value="3" {% if request.GET.rating == '3' %}selected{% endif %}>3+ Stars</option>
            <option value="2" {% if request.GET.rating == '2' %}selected{% endif %}>2+ Stars</option>
          </select>
        </div>
      </div>
      
      <div class="d-flex gap-2 mt-3">
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-search me-2"></i>Search
        </button>
        <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
          <i class="fas fa-times me-2"></i>Clear Filters
        </button>
      </div>
    </form>
  </div>

  <!-- Active Filters -->
  {% if active_filters %}
  <div class="mb-3">
    <h6 class="fw-semibold">Active Filters:</h6>
    <div class="d-flex flex-wrap">
      {% for filter in active_filters %}
        <span class="filter-chip">
          {{ filter.label }}: {{ filter.value }}
          <span class="remove" onclick="removeFilter('{{ filter.param }}', '{{ filter.value }}')">&times;</span>
        </span>
      {% endfor %}
    </div>
  </div>
  {% endif %}

  <!-- Search Statistics -->
  {% if results %}
  <div class="search-stats">
    <div class="row align-items-center">
      <div class="col-md-8">
        <h5 class="mb-1">
          <i class="fas fa-chart-bar me-2"></i>
          Found {{ total_results }} results{% if query %} for "{{ query }}"{% endif %}
        </h5>
        <p class="text-muted mb-0">
          Showing {{ results.start_index }}-{{ results.end_index }} of {{ results.paginator.count }} results
          ({{ search_time }}ms)
        </p>
      </div>
      <div class="col-md-4 text-end">
        <div class="btn-group" role="group">
          <button type="button" class="btn btn-outline-primary btn-sm" onclick="saveSearch()">
            <i class="fas fa-bookmark me-1"></i>Save Search
          </button>
          <button type="button" class="btn btn-outline-primary btn-sm" onclick="createAlert()">
            <i class="fas fa-bell me-1"></i>Create Alert
          </button>
        </div>
      </div>
    </div>
  </div>
  {% endif %}

  <!-- Search Results -->
  {% if results %}
    <div class="row">
      {% for result in results %}
        <div class="col-12">
          <div class="card search-result-card">
            <div class="position-relative">
              <span class="badge result-type-badge 
                {% if result.type == 'product' %}bg-primary
                {% elif result.type == 'store' %}bg-success
                {% elif result.type == 'brand' %}bg-warning
                {% endif %}">
                {{ result.type|title }}
              </span>
            </div>
            
            <div class="card-body">
              <div class="row align-items-center">
                <div class="col-md-2">
                  {% if result.image %}
                    <img src="{{ result.image }}" class="result-image" alt="{{ result.title }}">
                  {% else %}
                    <div class="result-image bg-light d-flex align-items-center justify-content-center">
                      <i class="fas fa-image fa-2x text-muted"></i>
                    </div>
                  {% endif %}
                </div>
                
                <div class="col-md-7">
                  <h5 class="card-title">
                    <a href="{{ result.url }}" class="text-decoration-none">{{ result.title }}</a>
                  </h5>
                  <p class="card-text text-muted">{{ result.description|truncatechars:150 }}</p>
                  
                  <div class="d-flex align-items-center gap-3">
                    {% if result.rating %}
                      <div class="d-flex align-items-center">
                        <div class="text-warning me-1">
                          {% for i in "12345" %}
                            {% if forloop.counter <= result.rating %}★{% else %}☆{% endif %}
                          {% endfor %}
                        </div>
                        <small class="text-muted">({{ result.review_count }} reviews)</small>
                      </div>
                    {% endif %}
                    
                    {% if result.category %}
                      <span class="badge bg-light text-dark">{{ result.category }}</span>
                    {% endif %}
                  </div>
                </div>
                
                <div class="col-md-3 text-end">
                  {% if result.price %}
                    <div class="mb-2">
                      <span class="h5 text-primary fw-bold">${{ result.price }}</span>
                      {% if result.original_price and result.original_price != result.price %}
                        <br><small class="text-muted text-decoration-line-through">${{ result.original_price }}</small>
                      {% endif %}
                    </div>
                  {% endif %}
                  
                  <div class="d-flex flex-column gap-2">
                    <a href="{{ result.url }}" class="btn btn-primary btn-sm">
                      <i class="fas fa-eye me-1"></i>View Details
                    </a>
                    {% if result.type == 'product' %}
                      <button class="btn btn-outline-primary btn-sm" onclick="addToCompare({{ result.id }})">
                        <i class="fas fa-balance-scale me-1"></i>Compare
                      </button>
                    {% endif %}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      {% endfor %}
    </div>

    <!-- Pagination -->
    {% if results.has_other_pages %}
    <nav aria-label="Search results pagination" class="mt-4">
      <ul class="pagination justify-content-center">
        {% if results.has_previous %}
          <li class="page-item">
            <a class="page-link" href="?{{ request.GET.urlencode }}&page={{ results.previous_page_number }}">
              <i class="fas fa-chevron-left"></i>
            </a>
          </li>
        {% endif %}
        
        {% for num in results.paginator.page_range %}
          {% if results.number == num %}
            <li class="page-item active">
              <span class="page-link">{{ num }}</span>
            </li>
          {% elif num > results.number|add:'-3' and num < results.number|add:'3' %}
            <li class="page-item">
              <a class="page-link" href="?{{ request.GET.urlencode }}&page={{ num }}">{{ num }}</a>
            </li>
          {% endif %}
        {% endfor %}
        
        {% if results.has_next %}
          <li class="page-item">
            <a class="page-link" href="?{{ request.GET.urlencode }}&page={{ results.next_page_number }}">
              <i class="fas fa-chevron-right"></i>
            </a>
          </li>
        {% endif %}
      </ul>
    </nav>
    {% endif %}

  {% else %}
    <!-- No Results -->
    <div class="no-results">
      <i class="fas fa-search fa-4x text-muted mb-3"></i>
      {% if query %}
        <h3>No results found for "{{ query }}"</h3>
        <p class="text-muted mb-4">Try adjusting your search terms or filters</p>
        
        <div class="d-flex justify-content-center gap-2 mb-4">
          <button class="btn btn-outline-primary" onclick="suggestAlternatives()">
            <i class="fas fa-lightbulb me-1"></i>Get Suggestions
          </button>
          <button class="btn btn-outline-primary" onclick="broadenSearch()">
            <i class="fas fa-expand me-1"></i>Broaden Search
          </button>
        </div>
      {% else %}
        <h3>Start Your Search</h3>
        <p class="text-muted mb-4">Enter keywords to find products, stores, and brands</p>
      {% endif %}
    </div>
  {% endif %}

  <!-- Trending Searches -->
  {% if not query or not results %}
  <div class="trending-searches">
    <h5 class="fw-bold mb-3">
      <i class="fas fa-fire me-2"></i>Trending Searches
    </h5>
    <div class="d-flex flex-wrap">
      <a href="?q=smartphone" class="trending-tag">smartphone</a>
      <a href="?q=laptop" class="trending-tag">laptop</a>
      <a href="?q=headphones" class="trending-tag">headphones</a>
      <a href="?q=gaming" class="trending-tag">gaming</a>
      <a href="?q=fashion" class="trending-tag">fashion</a>
      <a href="?q=home decor" class="trending-tag">home decor</a>
      <a href="?q=fitness" class="trending-tag">fitness</a>
      <a href="?q=books" class="trending-tag">books</a>
    </div>
  </div>
  {% endif %}
</div>

<script>
let suggestionTimeout;

function showSuggestions(query) {
  clearTimeout(suggestionTimeout);
  const suggestionsDiv = document.getElementById('suggestions');
  
  if (query.length < 2) {
    suggestionsDiv.style.display = 'none';
    return;
  }
  
  suggestionTimeout = setTimeout(() => {
    // Simulate API call for suggestions
    const suggestions = [
      'smartphone cases',
      'smartphone accessories',
      'smartphone chargers',
      'laptop bags',
      'laptop stands'
    ].filter(s => s.toLowerCase().includes(query.toLowerCase()));
    
    if (suggestions.length > 0) {
      suggestionsDiv.innerHTML = suggestions.map(s => 
        `<div class="suggestion-item" onclick="selectSuggestion('${s}')">${s}</div>`
      ).join('');
      suggestionsDiv.style.display = 'block';
    } else {
      suggestionsDiv.style.display = 'none';
    }
  }, 300);
}

function selectSuggestion(suggestion) {
  document.getElementById('searchQuery').value = suggestion;
  document.getElementById('suggestions').style.display = 'none';
  document.getElementById('searchForm').submit();
}

function clearFilters() {
  const form = document.getElementById('searchForm');
  const inputs = form.querySelectorAll('input, select');
  inputs.forEach(input => {
    if (input.name !== 'q') {
      input.value = '';
    }
  });
}

function removeFilter(param, value) {
  const url = new URL(window.location);
  url.searchParams.delete(param);
  window.location.href = url.toString();
}

function addToCompare(productId) {
  console.log('Add to compare:', productId);
  showToast('Added to comparison!', 'info');
}

function saveSearch() {
  // Save search functionality
  showToast('Search saved!', 'success');
}

function createAlert() {
  // Create search alert functionality
  showToast('Search alert created!', 'success');
}

function suggestAlternatives() {
  // Show search suggestions
  alert('Search suggestions would be implemented here');
}

function broadenSearch() {
  // Broaden search by removing some filters
  const url = new URL(window.location);
  url.searchParams.delete('category');
  url.searchParams.delete('min_price');
  url.searchParams.delete('max_price');
  window.location.href = url.toString();
}

function showToast(message, type) {
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} position-fixed`;
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
  toast.innerHTML = `
    ${message}
    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
  `;
  document.body.appendChild(toast);
  
  setTimeout(() => {
    if (toast.parentElement) {
      toast.remove();
    }
  }, 3000);
}

// Hide suggestions when clicking outside
document.addEventListener('click', function(e) {
  if (!e.target.closest('.search-autocomplete')) {
    document.getElementById('suggestions').style.display = 'none';
  }
});
</script>
{% endblock %}
