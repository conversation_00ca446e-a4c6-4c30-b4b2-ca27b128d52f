{% extends 'frontend/base.html' %}
{% load static %}

{% block title %}Store Dashboard - E-Commerce Hub{% endblock %}

{% block extra_css %}
<style>
    .store-header {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
    }

    .store-sidebar {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
        padding: 1.5rem;
        height: fit-content;
        position: sticky;
        top: 2rem;
    }

    .store-nav {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .store-nav li {
        margin-bottom: 0.5rem;
    }

    .store-nav a {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        color: var(--text-secondary);
        text-decoration: none;
        border-radius: var(--border-radius);
        transition: all 0.2s ease;
    }

    .store-nav a:hover,
    .store-nav a.active {
        background: var(--success-color);
        color: white;
    }

    .store-nav i {
        width: 20px;
        margin-right: 0.75rem;
    }

    .performance-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow-sm);
        border-left: 4px solid var(--success-color);
        transition: all 0.3s ease;
    }

    .performance-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .performance-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--success-color);
        display: block;
    }

    .performance-label {
        color: var(--text-secondary);
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .trend-indicator {
        font-size: 0.8rem;
        font-weight: 600;
    }

    .trend-up {
        color: var(--success-color);
    }

    .trend-down {
        color: var(--danger-color);
    }

    .sync-status {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow-sm);
        margin-bottom: 2rem;
    }

    .sync-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 0.5rem;
    }

    .sync-active {
        background: var(--success-color);
        animation: pulse 2s infinite;
    }

    .sync-error {
        background: var(--danger-color);
    }

    .sync-warning {
        background: var(--warning-color);
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    .product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1.5rem;
    }

    .product-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .product-image {
        width: 100%;
        height: 180px;
        object-fit: cover;
    }

    .product-info {
        padding: 1rem;
    }

    .product-title {
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .product-metrics {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-top: 0.5rem;
        font-size: 0.9rem;
    }

    .ai-score {
        background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
        color: white;
        padding: 0.2rem 0.5rem;
        border-radius: 10px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .chart-container {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow-sm);
        margin-bottom: 2rem;
    }

    .integration-card {
        background: white;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
    }

    .integration-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .integration-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: var(--success-color);
    }

    .integration-status {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-connected {
        background: rgba(16, 185, 129, 0.2);
        color: var(--success-color);
    }

    .status-disconnected {
        background: rgba(239, 68, 68, 0.2);
        color: var(--danger-color);
    }

    .status-syncing {
        background: rgba(245, 158, 11, 0.2);
        color: var(--warning-color);
    }

    .review-item {
        background: white;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.2s ease;
    }

    .review-item:hover {
        box-shadow: var(--shadow-md);
    }

    .review-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .review-rating {
        color: #fbbf24;
    }

    .sentiment-badge {
        padding: 0.2rem 0.5rem;
        border-radius: 10px;
        font-size: 0.7rem;
        font-weight: 600;
    }

    .sentiment-positive {
        background: rgba(16, 185, 129, 0.2);
        color: var(--success-color);
    }

    .sentiment-negative {
        background: rgba(239, 68, 68, 0.2);
        color: var(--danger-color);
    }

    .sentiment-neutral {
        background: rgba(100, 116, 139, 0.2);
        color: var(--secondary-color);
    }

    .store-content {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
        padding: 2rem;
        min-height: 600px;
    }

    @media (max-width: 768px) {
        .store-sidebar {
            position: static;
            margin-bottom: 2rem;
        }
        
        .store-nav {
            display: flex;
            overflow-x: auto;
            gap: 0.5rem;
        }
        
        .store-nav li {
            margin-bottom: 0;
            white-space: nowrap;
        }
        
        .product-grid {
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Store Header -->
<div class="store-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col">
                <h1><i class="fas fa-store me-2"></i>{{ store.name }} Dashboard</h1>
                <p class="mb-0">Manage your store and track performance</p>
            </div>
            <div class="col-auto">
                <div class="d-flex align-items-center gap-3">
                    <div class="text-end">
                        <div class="fw-bold">{{ user.username }}</div>
                        <small>Store Owner</small>
                    </div>
                    <div class="bg-white text-success rounded-circle d-flex align-items-center justify-content-center" 
                         style="width: 50px; height: 50px; font-size: 1.2rem;">
                        <i class="fas fa-user-tie"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <!-- Store Sidebar -->
        <div class="col-lg-3 col-md-4">
            <div class="store-sidebar">
                <ul class="store-nav">
                    <li>
                        <a href="#overview" class="nav-link active" data-section="overview">
                            <i class="fas fa-tachometer-alt"></i>
                            Overview
                        </a>
                    </li>
                    <li>
                        <a href="#products" class="nav-link" data-section="products">
                            <i class="fas fa-box"></i>
                            Products
                        </a>
                    </li>
                    <li>
                        <a href="#integration" class="nav-link" data-section="integration">
                            <i class="fas fa-plug"></i>
                            Integration
                        </a>
                    </li>
                    <li>
                        <a href="#reviews" class="nav-link" data-section="reviews">
                            <i class="fas fa-star"></i>
                            Reviews
                        </a>
                    </li>
                    <li>
                        <a href="#analytics" class="nav-link" data-section="analytics">
                            <i class="fas fa-chart-bar"></i>
                            Analytics
                        </a>
                    </li>
                    <li>
                        <a href="#settings" class="nav-link" data-section="settings">
                            <i class="fas fa-cog"></i>
                            Settings
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-lg-9 col-md-8">
            <div class="store-content">
                <!-- Overview Section -->
                <div id="overview-section" class="store-section">
                    <!-- Sync Status -->
                    <div class="sync-status">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5><i class="fas fa-sync me-2"></i>Integration Status</h5>
                                <div id="syncStatus">
                                    <span class="sync-indicator sync-active"></span>
                                    <span>Connected to {{ store.platform }}</span>
                                    <small class="text-muted ms-2">Last sync: 5 minutes ago</small>
                                </div>
                            </div>
                            <button class="btn btn-outline-success" id="syncNowBtn">
                                <i class="fas fa-sync me-2"></i>Sync Now
                            </button>
                        </div>
                    </div>

                    <!-- Performance Metrics -->
                    <div class="row g-4 mb-4">
                        <div class="col-lg-3 col-md-6">
                            <div class="performance-card">
                                <div class="performance-label">Total Products</div>
                                <span id="totalProducts" class="performance-value">0</span>
                                <div id="productsChange" class="trend-indicator">Loading...</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="performance-card">
                                <div class="performance-label">Average Rating</div>
                                <span id="averageRating" class="performance-value">0</span>
                                <div id="ratingChange" class="trend-indicator">Loading...</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="performance-card">
                                <div class="performance-label">Total Reviews</div>
                                <span id="totalReviews" class="performance-value">0</span>
                                <div id="reviewsChange" class="trend-indicator">Loading...</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="performance-card">
                                <div class="performance-label">Performance Score</div>
                                <span id="performanceScore" class="performance-value">0</span>
                                <div id="scoreChange" class="trend-indicator">Loading...</div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Row -->
                    <div class="row g-4">
                        <div class="col-lg-8">
                            <div class="chart-container">
                                <h5><i class="fas fa-chart-line me-2"></i>Performance Trends</h5>
                                <canvas id="performanceChart" style="height: 300px;"></canvas>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="chart-container">
                                <h5><i class="fas fa-star me-2"></i>Recent Reviews</h5>
                                <div id="recentReviews">
                                    <div class="text-center">
                                        <div class="spinner-border text-success" role="status"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Products Section -->
                <div id="products-section" class="store-section d-none">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3><i class="fas fa-box me-2"></i>Product Management</h3>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-success" id="syncProductsBtn">
                                <i class="fas fa-sync me-2"></i>Sync Products
                            </button>
                            <button class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Add Product
                            </button>
                        </div>
                    </div>

                    <div id="productsGrid" class="product-grid">
                        <div class="col-12 text-center">
                            <div class="spinner-border text-success" role="status"></div>
                        </div>
                    </div>
                </div>

                <!-- Integration Section -->
                <div id="integration-section" class="store-section d-none">
                    <h3><i class="fas fa-plug me-2"></i>Platform Integration</h3>
                    <p class="text-muted mb-4">Manage your store connections and sync settings</p>

                    <div class="row g-4">
                        <div class="col-md-4">
                            <div class="integration-card">
                                <div class="integration-icon">
                                    <i class="fab fa-shopify"></i>
                                </div>
                                <h5>Shopify</h5>
                                <div class="integration-status status-connected">Connected</div>
                                <div class="mt-3">
                                    <button class="btn btn-sm btn-outline-success">Configure</button>
                                    <button class="btn btn-sm btn-outline-danger">Disconnect</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="integration-card">
                                <div class="integration-icon">
                                    <i class="fab fa-wordpress"></i>
                                </div>
                                <h5>WooCommerce</h5>
                                <div class="integration-status status-disconnected">Not Connected</div>
                                <div class="mt-3">
                                    <button class="btn btn-sm btn-success">Connect</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="integration-card">
                                <div class="integration-icon">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <h5>Magento</h5>
                                <div class="integration-status status-disconnected">Not Connected</div>
                                <div class="mt-3">
                                    <button class="btn btn-sm btn-success">Connect</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reviews Section -->
                <div id="reviews-section" class="store-section d-none">
                    <h3><i class="fas fa-star me-2"></i>Customer Reviews</h3>
                    <p class="text-muted mb-4">Monitor and respond to customer feedback</p>

                    <div id="reviewsList">
                        <div class="text-center">
                            <div class="spinner-border text-success" role="status"></div>
                        </div>
                    </div>
                </div>

                <!-- Analytics Section -->
                <div id="analytics-section" class="store-section d-none">
                    <h3><i class="fas fa-chart-bar me-2"></i>Store Analytics</h3>
                    <p class="text-muted mb-4">Detailed performance insights and trends</p>
                    
                    <div class="row g-4">
                        <div class="col-lg-6">
                            <div class="chart-container">
                                <h5>Product Performance</h5>
                                <canvas id="productChart" style="height: 250px;"></canvas>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="chart-container">
                                <h5>Customer Satisfaction</h5>
                                <canvas id="satisfactionChart" style="height: 250px;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Section -->
                <div id="settings-section" class="store-section d-none">
                    <h3><i class="fas fa-cog me-2"></i>Store Settings</h3>
                    <p class="text-muted mb-4">Configure your store preferences and settings</p>
                    
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Store Information</h5>
                                </div>
                                <div class="card-body">
                                    <form id="storeInfoForm">
                                        <div class="mb-3">
                                            <label for="storeName" class="form-label">Store Name</label>
                                            <input type="text" class="form-control" id="storeName" 
                                                   value="{{ store.name }}">
                                        </div>
                                        <div class="mb-3">
                                            <label for="storeDescription" class="form-label">Description</label>
                                            <textarea class="form-control" id="storeDescription" rows="3">{{ store.description }}</textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label for="storeUrl" class="form-label">Store URL</label>
                                            <input type="url" class="form-control" id="storeUrl" 
                                                   value="{{ store.url }}">
                                        </div>
                                        <button type="submit" class="btn btn-success">
                                            Update Information
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Sync Settings</h5>
                                </div>
                                <div class="card-body">
                                    <form id="syncSettingsForm">
                                        <div class="mb-3">
                                            <label class="form-label">Auto Sync</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       id="autoSyncProducts" checked>
                                                <label class="form-check-label" for="autoSyncProducts">
                                                    Sync products automatically
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       id="autoSyncPrices" checked>
                                                <label class="form-check-label" for="autoSyncPrices">
                                                    Sync prices automatically
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       id="autoSyncInventory">
                                                <label class="form-check-label" for="autoSyncInventory">
                                                    Sync inventory automatically
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="syncFrequency" class="form-label">Sync Frequency</label>
                                            <select class="form-select" id="syncFrequency">
                                                <option value="5">Every 5 minutes</option>
                                                <option value="15" selected>Every 15 minutes</option>
                                                <option value="30">Every 30 minutes</option>
                                                <option value="60">Every hour</option>
                                            </select>
                                        </div>
                                        
                                        <button type="submit" class="btn btn-success">
                                            Save Settings
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let storeData = null;
let performanceChart = null;

document.addEventListener('DOMContentLoaded', function() {
    loadStoreDashboard();
    setupNavigation();
    setupEventListeners();
});

function loadStoreDashboard() {
    // This would typically load from store-specific APIs
    // For now, we'll simulate the data
    setTimeout(() => {
        const mockData = {
            metrics: {
                total_products: 1250,
                products_change: 3.2,
                average_rating: 4.6,
                rating_change: 0.1,
                total_reviews: 342,
                reviews_change: 12.5,
                performance_score: 4.8,
                score_change: 0.2
            },
            recent_reviews: [
                {
                    product_name: 'Wireless Headphones',
                    rating: 5,
                    comment: 'Excellent quality and fast shipping!',
                    customer: 'John D.',
                    sentiment: 'positive',
                    date: '2024-01-15'
                },
                {
                    product_name: 'Smart Watch',
                    rating: 4,
                    comment: 'Good product, could be better.',
                    customer: 'Sarah M.',
                    sentiment: 'neutral',
                    date: '2024-01-14'
                }
            ]
        };
        
        renderStoreData(mockData);
        initializeCharts();
    }, 1000);
}

function renderStoreData(data) {
    // Update metrics
    document.getElementById('totalProducts').textContent = data.metrics.total_products.toLocaleString();
    document.getElementById('productsChange').textContent = `+${data.metrics.products_change}% this month`;
    document.getElementById('productsChange').className = `trend-indicator ${data.metrics.products_change > 0 ? 'trend-up' : 'trend-down'}`;
    
    document.getElementById('averageRating').textContent = data.metrics.average_rating.toFixed(1);
    document.getElementById('ratingChange').textContent = `+${data.metrics.rating_change} this month`;
    document.getElementById('ratingChange').className = `trend-indicator ${data.metrics.rating_change > 0 ? 'trend-up' : 'trend-down'}`;
    
    document.getElementById('totalReviews').textContent = data.metrics.total_reviews;
    document.getElementById('reviewsChange').textContent = `+${data.metrics.reviews_change}% this month`;
    document.getElementById('reviewsChange').className = `trend-indicator ${data.metrics.reviews_change > 0 ? 'trend-up' : 'trend-down'}`;
    
    document.getElementById('performanceScore').textContent = data.metrics.performance_score.toFixed(1);
    document.getElementById('scoreChange').textContent = `+${data.metrics.score_change} this month`;
    document.getElementById('scoreChange').className = `trend-indicator ${data.metrics.score_change > 0 ? 'trend-up' : 'trend-down'}`;
    
    // Render recent reviews
    renderRecentReviews(data.recent_reviews);
}

function renderRecentReviews(reviews) {
    const container = document.getElementById('recentReviews');
    
    if (!reviews || reviews.length === 0) {
        container.innerHTML = '<p class="text-muted">No recent reviews</p>';
        return;
    }
    
    container.innerHTML = reviews.map(review => `
        <div class="review-item mb-3">
            <div class="d-flex justify-content-between align-items-start mb-2">
                <div>
                    <h6 class="mb-1">${review.product_name}</h6>
                    <div class="review-rating">
                        ${'★'.repeat(review.rating)}${'☆'.repeat(5-review.rating)}
                    </div>
                </div>
                <span class="sentiment-badge sentiment-${review.sentiment}">
                    ${review.sentiment}
                </span>
            </div>
            <p class="small mb-1">${review.comment}</p>
            <small class="text-muted">by ${review.customer} • ${utils.formatDate(review.date)}</small>
        </div>
    `).join('');
}

function setupNavigation() {
    const navLinks = document.querySelectorAll('.store-nav .nav-link');
    const sections = document.querySelectorAll('.store-section');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update active nav link
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            
            // Show corresponding section
            const sectionId = this.dataset.section + '-section';
            sections.forEach(section => {
                section.classList.add('d-none');
            });
            document.getElementById(sectionId).classList.remove('d-none');
            
            // Load section-specific data
            loadSectionData(this.dataset.section);
        });
    });
}

function setupEventListeners() {
    // Sync now button
    document.getElementById('syncNowBtn').addEventListener('click', function() {
        utils.showLoading(this);
        
        // Simulate sync process
        setTimeout(() => {
            utils.hideLoading(this);
            utils.showAlert('Products synced successfully', 'success');
        }, 2000);
    });
    
    // Store info form
    document.getElementById('storeInfoForm').addEventListener('submit', function(e) {
        e.preventDefault();
        utils.showAlert('Store information updated successfully', 'success');
    });
    
    // Sync settings form
    document.getElementById('syncSettingsForm').addEventListener('submit', function(e) {
        e.preventDefault();
        utils.showAlert('Sync settings saved successfully', 'success');
    });
}

function loadSectionData(section) {
    switch(section) {
        case 'products':
            loadProductsData();
            break;
        case 'reviews':
            loadReviewsData();
            break;
        case 'analytics':
            loadAnalyticsData();
            break;
    }
}

function loadProductsData() {
    const container = document.getElementById('productsGrid');
    
    // Mock products data
    setTimeout(() => {
        const products = [
            {
                id: 1,
                name: 'Wireless Bluetooth Headphones',
                price: 99.99,
                image: '/static/images/product-placeholder.jpg',
                rating: 4.5,
                ai_score: 4.2,
                views: 1250,
                sales: 89
            },
            {
                id: 2,
                name: 'Smart Fitness Watch',
                price: 199.99,
                image: '/static/images/product-placeholder.jpg',
                rating: 4.3,
                ai_score: 4.0,
                views: 890,
                sales: 56
            }
        ];
        
        container.innerHTML = products.map(product => `
            <div class="product-card">
                <img src="${product.image}" class="product-image" alt="${product.name}">
                <div class="product-info">
                    <h6 class="product-title">${product.name}</h6>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-bold text-success">${utils.formatCurrency(product.price)}</span>
                        <div class="ai-score">AI: ${product.ai_score}/5</div>
                    </div>
                    <div class="product-metrics">
                        <small class="text-muted">
                            <i class="fas fa-eye me-1"></i>${product.views} views
                        </small>
                        <small class="text-muted">
                            <i class="fas fa-shopping-cart me-1"></i>${product.sales} sales
                        </small>
                    </div>
                    <div class="rating-stars small mt-1">
                        ${'★'.repeat(Math.floor(product.rating))}${'☆'.repeat(5-Math.floor(product.rating))}
                        <span class="text-muted ms-1">(${product.rating})</span>
                    </div>
                </div>
            </div>
        `).join('');
    }, 500);
}

function loadReviewsData() {
    const container = document.getElementById('reviewsList');
    
    // Mock reviews data
    setTimeout(() => {
        const reviews = [
            {
                product_name: 'Wireless Bluetooth Headphones',
                customer: 'John Smith',
                rating: 5,
                title: 'Excellent quality!',
                comment: 'These headphones exceeded my expectations. Great sound quality and comfortable to wear.',
                sentiment: 'positive',
                date: '2024-01-15',
                verified: true
            },
            {
                product_name: 'Smart Fitness Watch',
                customer: 'Sarah Johnson',
                rating: 4,
                title: 'Good but could be better',
                comment: 'The watch works well but the battery life could be improved.',
                sentiment: 'neutral',
                date: '2024-01-14',
                verified: true
            }
        ];
        
        container.innerHTML = reviews.map(review => `
            <div class="review-item">
                <div class="review-header">
                    <div>
                        <h6>${review.product_name}</h6>
                        <div class="d-flex align-items-center gap-2">
                            <div class="review-rating">
                                ${'★'.repeat(review.rating)}${'☆'.repeat(5-review.rating)}
                            </div>
                            <span class="sentiment-badge sentiment-${review.sentiment}">
                                ${review.sentiment}
                            </span>
                            ${review.verified ? '<span class="badge bg-success">Verified</span>' : ''}
                        </div>
                    </div>
                    <div class="text-end">
                        <small class="text-muted">${utils.formatDate(review.date)}</small>
                    </div>
                </div>
                
                <h6>${review.title}</h6>
                <p class="mb-2">${review.comment}</p>
                <small class="text-muted">by ${review.customer}</small>
                
                <div class="mt-3">
                    <button class="btn btn-sm btn-outline-success">
                        <i class="fas fa-reply me-1"></i>Reply
                    </button>
                    <button class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-flag me-1"></i>Report
                    </button>
                </div>
            </div>
        `).join('');
    }, 500);
}

function loadAnalyticsData() {
    // Initialize additional charts for analytics section
    setTimeout(() => {
        initializeAnalyticsCharts();
    }, 500);
}

function initializeCharts() {
    // Performance chart
    const ctx = document.getElementById('performanceChart').getContext('2d');
    
    performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [
                {
                    label: 'Rating',
                    data: [4.2, 4.3, 4.4, 4.5, 4.6, 4.6],
                    borderColor: 'rgb(16, 185, 129)',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Sales',
                    data: [120, 150, 180, 220, 280, 320],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    beginAtZero: true,
                    max: 5
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    beginAtZero: true,
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
}

function initializeAnalyticsCharts() {
    // Product performance chart
    const productCtx = document.getElementById('productChart').getContext('2d');
    new Chart(productCtx, {
        type: 'doughnut',
        data: {
            labels: ['Electronics', 'Clothing', 'Home & Garden', 'Sports'],
            datasets: [{
                data: [45, 25, 20, 10],
                backgroundColor: [
                    'rgb(16, 185, 129)',
                    'rgb(59, 130, 246)',
                    'rgb(245, 158, 11)',
                    'rgb(239, 68, 68)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    
    // Customer satisfaction chart
    const satisfactionCtx = document.getElementById('satisfactionChart').getContext('2d');
    new Chart(satisfactionCtx, {
        type: 'bar',
        data: {
            labels: ['5 Stars', '4 Stars', '3 Stars', '2 Stars', '1 Star'],
            datasets: [{
                label: 'Reviews',
                data: [180, 120, 45, 15, 8],
                backgroundColor: 'rgba(16, 185, 129, 0.8)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}
</script>
{% endblock %}
