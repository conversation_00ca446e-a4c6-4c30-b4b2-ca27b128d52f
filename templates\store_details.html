{% extends 'base.html' %}
{% load static %}

{% block title %}{{ store.name }} - Store Details - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="{{ store.description|truncatechars:160 }}">
<meta property="og:title" content="{{ store.name }} - Best in Click">
<meta property="og:description" content="{{ store.description|truncatechars:160 }}">
<meta property="og:image" content="{{ store.logo }}">
<style>
  .store-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
  }
  .store-logo {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 50%;
    border: 4px solid white;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  }
  .store-rating {
    background: #ffc107;
    color: #000;
    border-radius: 2rem;
    padding: 0.5rem 1rem;
    font-size: 1.1rem;
    font-weight: bold;
    display: inline-flex;
    align-items: center;
  }
  .verified-badge {
    background: #28a745;
    color: white;
    border-radius: 2rem;
    padding: 0.5rem 1rem;
    font-weight: bold;
    display: inline-flex;
    align-items: center;
  }
  .stats-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    height: 100%;
  }
  .stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: #0d6efd;
  }
  .product-card {
    border: none;
    border-radius: 1rem;
    transition: all 0.3s ease;
    height: 100%;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }
  .product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.15);
  }
  .product-image {
    height: 180px;
    object-fit: cover;
    border-radius: 1rem 1rem 0 0;
  }
  .review-card {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
  }
  .review-rating {
    color: #ffc107;
  }
  .contact-card {
    background: #e3f0ff;
    border-radius: 1rem;
    padding: 1.5rem;
  }
  .policy-card {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
  }
  .breadcrumb-custom {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
  }
  .store-actions {
    position: sticky;
    top: 100px;
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  }
</style>
{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<div class="container mt-3">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-custom">
      <li class="breadcrumb-item"><a href="/">Home</a></li>
      <li class="breadcrumb-item"><a href="/stores/">Stores</a></li>
      <li class="breadcrumb-item active">{{ store.name }}</li>
    </ol>
  </nav>
</div>

<div class="container">
  <!-- Store Header -->
  <div class="store-header">
    <div class="row align-items-center">
      <div class="col-md-2">
        {% if store.logo %}
          <img src="{{ store.logo }}" class="store-logo" alt="{{ store.name }}">
        {% else %}
          <div class="store-logo bg-primary d-flex align-items-center justify-content-center text-white">
            <i class="fas fa-store fa-3x"></i>
          </div>
        {% endif %}
      </div>
      
      <div class="col-md-7">
        <div class="d-flex align-items-center mb-2">
          <h1 class="fw-bold me-3">{{ store.name }}</h1>
          {% if store.is_verified %}
            <span class="verified-badge">
              <i class="fas fa-check-circle me-1"></i>Verified
            </span>
          {% endif %}
        </div>
        
        <div class="d-flex align-items-center mb-3">
          <div class="store-rating me-3">
            {{ store.rating }} <i class="fas fa-star"></i>
          </div>
          <span class="text-muted">({{ store.review_count }} reviews)</span>
        </div>
        
        <p class="text-muted mb-2">{{ store.description }}</p>
        
        <div class="d-flex align-items-center text-muted">
          <i class="fas fa-map-marker-alt me-2"></i>
          <span>{{ store.location }}</span>
          <i class="fas fa-clock ms-3 me-2"></i>
          <span>Member since {{ store.created_at|date:"Y" }}</span>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="store-actions">
          <div class="d-grid gap-2">
            <button class="btn btn-primary" onclick="followStore({{ store.id }})">
              <i class="fas fa-heart me-2"></i>Follow Store
            </button>
            <button class="btn btn-outline-primary" onclick="shareStore()">
              <i class="fas fa-share-alt me-2"></i>Share Store
            </button>
            <button class="btn btn-outline-secondary" onclick="reportStore()">
              <i class="fas fa-flag me-2"></i>Report
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Store Statistics -->
  <div class="row mb-4">
    <div class="col-md-3 mb-3">
      <div class="stats-card">
        <div class="stats-number">{{ store.product_count }}</div>
        <h6>Products</h6>
        <p class="text-muted small mb-0">Available items</p>
      </div>
    </div>
    <div class="col-md-3 mb-3">
      <div class="stats-card">
        <div class="stats-number">{{ store.order_count }}</div>
        <h6>Orders</h6>
        <p class="text-muted small mb-0">Successfully completed</p>
      </div>
    </div>
    <div class="col-md-3 mb-3">
      <div class="stats-card">
        <div class="stats-number">{{ store.response_time }}h</div>
        <h6>Response Time</h6>
        <p class="text-muted small mb-0">Average reply time</p>
      </div>
    </div>
    <div class="col-md-3 mb-3">
      <div class="stats-card">
        <div class="stats-number">{{ store.satisfaction_rate }}%</div>
        <h6>Satisfaction</h6>
        <p class="text-muted small mb-0">Customer satisfaction</p>
      </div>
    </div>
  </div>

  <!-- Store Tabs -->
  <ul class="nav nav-tabs mb-4" id="storeTabs" role="tablist">
    <li class="nav-item" role="presentation">
      <button class="nav-link active" id="products-tab" data-bs-toggle="tab" data-bs-target="#products" type="button">
        Products ({{ store.product_count }})
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button class="nav-link" id="about-tab" data-bs-toggle="tab" data-bs-target="#about" type="button">
        About Store
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button">
        Reviews ({{ store.review_count }})
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button class="nav-link" id="policies-tab" data-bs-toggle="tab" data-bs-target="#policies" type="button">
        Policies
      </button>
    </li>
  </ul>

  <div class="tab-content" id="storeTabsContent">
    <!-- Products Tab -->
    <div class="tab-pane fade show active" id="products" role="tabpanel">
      <!-- Product Filters -->
      <div class="row mb-4">
        <div class="col-md-6">
          <input type="text" class="form-control" placeholder="Search products in this store..." id="productSearch">
        </div>
        <div class="col-md-3">
          <select class="form-select" id="categoryFilter">
            <option value="">All Categories</option>
            {% for category in store_categories %}
              <option value="{{ category.id }}">{{ category.name }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="col-md-3">
          <select class="form-select" id="sortProducts">
            <option value="name">Name A-Z</option>
            <option value="-name">Name Z-A</option>
            <option value="price">Price Low to High</option>
            <option value="-price">Price High to Low</option>
            <option value="-rating">Highest Rated</option>
          </select>
        </div>
      </div>

      <!-- Products Grid -->
      <div class="row">
        {% for product in store_products %}
        <div class="col-lg-3 col-md-4 col-6 mb-4">
          <div class="card product-card">
            {% if product.image_url %}
              <img src="{{ product.image_url }}" class="card-img-top product-image" alt="{{ product.name }}">
            {% else %}
              <div class="product-image bg-light d-flex align-items-center justify-content-center">
                <i class="fas fa-image fa-3x text-muted"></i>
              </div>
            {% endif %}
            
            <div class="card-body">
              <h6 class="card-title">
                <a href="/products/{{ product.id }}/" class="text-decoration-none text-dark">
                  {{ product.name|truncatechars:40 }}
                </a>
              </h6>
              
              <div class="d-flex align-items-center mb-2">
                <div class="text-warning me-2">
                  {% for i in "12345" %}
                    {% if forloop.counter <= product.rating %}★{% else %}☆{% endif %}
                  {% endfor %}
                </div>
                <small class="text-muted">({{ product.review_count }})</small>
              </div>
              
              <div class="d-flex justify-content-between align-items-center">
                <span class="h6 text-primary fw-bold">${{ product.price }}</span>
                {% if product.original_price and product.original_price != product.price %}
                  <small class="text-muted text-decoration-line-through">${{ product.original_price }}</small>
                {% endif %}
              </div>
            </div>
            
            <div class="card-footer bg-transparent">
              <a href="/products/{{ product.id }}/" class="btn btn-primary btn-sm w-100">
                <i class="fas fa-eye me-1"></i>View Details
              </a>
            </div>
          </div>
        </div>
        {% empty %}
        <div class="col-12">
          <div class="text-center py-5">
            <i class="fas fa-box-open fa-4x text-muted mb-3"></i>
            <h4>No products found</h4>
            <p class="text-muted">This store hasn't added any products yet.</p>
          </div>
        </div>
        {% endfor %}
      </div>

      <!-- Load More Products -->
      {% if store_products %}
      <div class="text-center mt-4">
        <button class="btn btn-outline-primary" onclick="loadMoreProducts()">
          <i class="fas fa-plus me-2"></i>Load More Products
        </button>
      </div>
      {% endif %}
    </div>

    <!-- About Tab -->
    <div class="tab-pane fade" id="about" role="tabpanel">
      <div class="row">
        <div class="col-lg-8">
          <h4 class="fw-bold mb-3">About {{ store.name }}</h4>
          <p>{{ store.description }}</p>
          
          <h5 class="fw-bold mt-4 mb-3">Store Information</h5>
          <div class="row">
            <div class="col-md-6">
              <ul class="list-unstyled">
                <li class="mb-2"><strong>Founded:</strong> {{ store.created_at|date:"Y" }}</li>
                <li class="mb-2"><strong>Location:</strong> {{ store.location }}</li>
                <li class="mb-2"><strong>Store Type:</strong> {{ store.store_type|default:"General" }}</li>
                <li class="mb-2"><strong>Specialties:</strong> {{ store.specialties|default:"Various products" }}</li>
              </ul>
            </div>
            <div class="col-md-6">
              <ul class="list-unstyled">
                <li class="mb-2"><strong>Products:</strong> {{ store.product_count }} items</li>
                <li class="mb-2"><strong>Rating:</strong> {{ store.rating }}/5 stars</li>
                <li class="mb-2"><strong>Reviews:</strong> {{ store.review_count }} reviews</li>
                <li class="mb-2"><strong>Response Time:</strong> {{ store.response_time|default:"24" }} hours</li>
              </ul>
            </div>
          </div>
        </div>
        
        <div class="col-lg-4">
          <div class="contact-card">
            <h5 class="fw-bold mb-3">Contact Information</h5>
            {% if store.email %}
              <p class="mb-2">
                <i class="fas fa-envelope me-2"></i>
                <a href="mailto:{{ store.email }}">{{ store.email }}</a>
              </p>
            {% endif %}
            {% if store.phone %}
              <p class="mb-2">
                <i class="fas fa-phone me-2"></i>
                <a href="tel:{{ store.phone }}">{{ store.phone }}</a>
              </p>
            {% endif %}
            {% if store.website %}
              <p class="mb-2">
                <i class="fas fa-globe me-2"></i>
                <a href="{{ store.website }}" target="_blank">Visit Website</a>
              </p>
            {% endif %}
            <p class="mb-2">
              <i class="fas fa-map-marker-alt me-2"></i>
              {{ store.address|default:store.location }}
            </p>
            
            <hr>
            
            <h6 class="fw-bold mb-2">Business Hours</h6>
            <small class="text-muted">
              Monday - Friday: 9:00 AM - 6:00 PM<br>
              Saturday: 10:00 AM - 4:00 PM<br>
              Sunday: Closed
            </small>
          </div>
        </div>
      </div>
    </div>

    <!-- Reviews Tab -->
    <div class="tab-pane fade" id="reviews" role="tabpanel">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h4 class="fw-bold">Customer Reviews</h4>
        <button class="btn btn-primary" onclick="writeReview()">Write a Review</button>
      </div>
      
      <!-- Review Summary -->
      <div class="row mb-4">
        <div class="col-md-4">
          <div class="text-center">
            <div class="display-4 fw-bold text-primary">{{ store.rating }}</div>
            <div class="text-warning mb-2">
              {% for i in "12345" %}
                {% if forloop.counter <= store.rating %}★{% else %}☆{% endif %}
              {% endfor %}
            </div>
            <p class="text-muted">Based on {{ store.review_count }} reviews</p>
          </div>
        </div>
        <div class="col-md-8">
          <!-- Rating breakdown would go here -->
          <div class="mb-2">
            <div class="d-flex align-items-center">
              <span class="me-2">5 stars</span>
              <div class="progress flex-fill me-2">
                <div class="progress-bar bg-warning" style="width: 70%"></div>
              </div>
              <span class="text-muted">70%</span>
            </div>
          </div>
          <!-- More rating breakdowns... -->
        </div>
      </div>
      
      <!-- Reviews List -->
      <div class="reviews-list">
        {% for review in store_reviews %}
        <div class="review-card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-start mb-2">
              <div>
                <h6 class="mb-1">{{ review.user.username }}</h6>
                <div class="review-rating">
                  {% for i in "12345" %}
                    {% if forloop.counter <= review.rating %}★{% else %}☆{% endif %}
                  {% endfor %}
                </div>
              </div>
              <small class="text-muted">{{ review.created_at|timesince }} ago</small>
            </div>
            <p class="mb-0">{{ review.text }}</p>
          </div>
        </div>
        {% empty %}
        <div class="text-center py-4">
          <i class="fas fa-comments fa-3x text-muted mb-3"></i>
          <h5>No reviews yet</h5>
          <p class="text-muted">Be the first to review this store!</p>
        </div>
        {% endfor %}
      </div>
      
      {% if store_reviews %}
      <div class="text-center mt-4">
        <button class="btn btn-outline-primary" onclick="loadMoreReviews()">Load More Reviews</button>
      </div>
      {% endif %}
    </div>

    <!-- Policies Tab -->
    <div class="tab-pane fade" id="policies" role="tabpanel">
      <div class="row">
        <div class="col-md-6">
          <div class="policy-card">
            <h5 class="fw-bold mb-3">
              <i class="fas fa-shipping-fast text-primary me-2"></i>Shipping Policy
            </h5>
            <ul class="list-unstyled">
              <li class="mb-2">• Free shipping on orders over $50</li>
              <li class="mb-2">• Standard delivery: 3-5 business days</li>
              <li class="mb-2">• Express delivery: 1-2 business days</li>
              <li class="mb-2">• International shipping available</li>
            </ul>
          </div>
          
          <div class="policy-card">
            <h5 class="fw-bold mb-3">
              <i class="fas fa-undo text-success me-2"></i>Return Policy
            </h5>
            <ul class="list-unstyled">
              <li class="mb-2">• 30-day return window</li>
              <li class="mb-2">• Items must be in original condition</li>
              <li class="mb-2">• Free return shipping</li>
              <li class="mb-2">• Refund processed within 5-7 days</li>
            </ul>
          </div>
        </div>
        
        <div class="col-md-6">
          <div class="policy-card">
            <h5 class="fw-bold mb-3">
              <i class="fas fa-shield-alt text-warning me-2"></i>Warranty Policy
            </h5>
            <ul class="list-unstyled">
              <li class="mb-2">• Manufacturer warranty included</li>
              <li class="mb-2">• Extended warranty available</li>
              <li class="mb-2">• Defective items replaced immediately</li>
              <li class="mb-2">• 24/7 customer support</li>
            </ul>
          </div>
          
          <div class="policy-card">
            <h5 class="fw-bold mb-3">
              <i class="fas fa-credit-card text-info me-2"></i>Payment Policy
            </h5>
            <ul class="list-unstyled">
              <li class="mb-2">• All major credit cards accepted</li>
              <li class="mb-2">• PayPal and digital wallets supported</li>
              <li class="mb-2">• Secure SSL encryption</li>
              <li class="mb-2">• No hidden fees or charges</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function followStore(storeId) {
  console.log('Follow store:', storeId);
  showToast('Store followed successfully!', 'success');
}

function shareStore() {
  if (navigator.share) {
    navigator.share({
      title: '{{ store.name }}',
      text: '{{ store.description|truncatechars:100 }}',
      url: window.location.href
    });
  } else {
    navigator.clipboard.writeText(window.location.href);
    showToast('Store link copied to clipboard!', 'success');
  }
}

function reportStore() {
  alert('Report functionality would be implemented here');
}

function writeReview() {
  alert('Review functionality would be implemented here');
}

function loadMoreProducts() {
  console.log('Loading more products...');
}

function loadMoreReviews() {
  console.log('Loading more reviews...');
}

function showToast(message, type) {
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} position-fixed`;
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
  toast.innerHTML = `
    ${message}
    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
  `;
  document.body.appendChild(toast);
  
  setTimeout(() => {
    if (toast.parentElement) {
      toast.remove();
    }
  }, 3000);
}

// Product search and filter functionality
document.getElementById('productSearch').addEventListener('input', function() {
  // Implement product search
  console.log('Searching products:', this.value);
});

document.getElementById('categoryFilter').addEventListener('change', function() {
  // Implement category filter
  console.log('Filter by category:', this.value);
});

document.getElementById('sortProducts').addEventListener('change', function() {
  // Implement product sorting
  console.log('Sort products by:', this.value);
});
</script>
{% endblock %}
