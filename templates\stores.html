{% extends 'base.html' %}
{% load static %}

{% block title %}Stores Directory - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="Discover thousands of trusted stores on Best in Click. Browse by category, location, and rating to find the perfect shopping destination.">
<style>
  .stores-hero {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
  }
  .store-card {
    border: none;
    border-radius: 1rem;
    transition: all 0.3s ease;
    height: 100%;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }
  .store-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.15);
  }
  .store-logo {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 50%;
    border: 3px solid #f8f9fa;
  }
  .store-rating {
    background: #ffc107;
    color: #000;
    border-radius: 2rem;
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    font-weight: bold;
  }
  .store-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 10;
  }
  .verified-badge {
    background: #28a745;
    color: white;
    border-radius: 2rem;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: bold;
  }
  .filter-sidebar {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    height: fit-content;
    position: sticky;
    top: 100px;
  }
  .filter-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #dee2e6;
  }
  .filter-section:last-child {
    border-bottom: none;
  }
  .stats-card {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border: none;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 2rem;
  }
  .stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: #0d6efd;
  }
  .featured-stores {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
  }
  .map-view {
    height: 400px;
    background: #e9ecef;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
  }
  .view-toggle {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
  }
  .view-toggle .btn {
    border: none;
    background: transparent;
  }
  .view-toggle .btn.active {
    background: #0d6efd;
    color: white;
  }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="stores-hero">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-8">
        <h1 class="display-4 fw-bold mb-4">Discover Amazing Stores</h1>
        <p class="lead mb-4">Browse thousands of trusted retailers from around the world. Find your favorite brands and discover new shopping destinations.</p>
        <div class="d-flex gap-3">
          <a href="#featured" class="btn btn-light btn-lg">Featured Stores</a>
          <a href="#directory" class="btn btn-outline-light btn-lg">Browse All</a>
        </div>
      </div>
      <div class="col-lg-4 text-center">
        <i class="fas fa-store" style="font-size: 6rem; opacity: 0.3;"></i>
      </div>
    </div>
  </div>
</div>

<div class="container">
  <!-- Store Statistics -->
  <div class="row mb-4">
    <div class="col-md-3 mb-3">
      <div class="stats-card">
        <div class="stats-number">50K+</div>
        <h6>Total Stores</h6>
        <p class="text-muted small mb-0">Worldwide partners</p>
      </div>
    </div>
    <div class="col-md-3 mb-3">
      <div class="stats-card">
        <div class="stats-number">180+</div>
        <h6>Countries</h6>
        <p class="text-muted small mb-0">Global coverage</p>
      </div>
    </div>
    <div class="col-md-3 mb-3">
      <div class="stats-card">
        <div class="stats-number">4.8</div>
        <h6>Avg Rating</h6>
        <p class="text-muted small mb-0">Customer satisfaction</p>
      </div>
    </div>
    <div class="col-md-3 mb-3">
      <div class="stats-card">
        <div class="stats-number">99.9%</div>
        <h6>Uptime</h6>
        <p class="text-muted small mb-0">Service reliability</p>
      </div>
    </div>
  </div>

  <!-- Featured Stores -->
  <div class="featured-stores" id="featured">
    <div class="text-center mb-4">
      <h2 class="fw-bold text-primary">⭐ Featured Stores</h2>
      <p class="text-muted">Hand-picked stores with exceptional service and products</p>
    </div>
    
    <div class="row">
      <div class="col-lg-2 col-md-4 col-6 mb-3">
        <div class="card store-card text-center">
          <div class="card-body p-3">
            <div class="store-logo bg-primary d-flex align-items-center justify-content-center text-white mx-auto mb-2">
              <i class="fas fa-laptop fa-2x"></i>
            </div>
            <h6 class="card-title mb-1">TechWorld</h6>
            <div class="store-rating mb-2">4.9 ★</div>
            <small class="text-muted">Electronics</small>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-4 col-6 mb-3">
        <div class="card store-card text-center">
          <div class="card-body p-3">
            <div class="store-logo bg-success d-flex align-items-center justify-content-center text-white mx-auto mb-2">
              <i class="fas fa-tshirt fa-2x"></i>
            </div>
            <h6 class="card-title mb-1">FashionHub</h6>
            <div class="store-rating mb-2">4.8 ★</div>
            <small class="text-muted">Fashion</small>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-4 col-6 mb-3">
        <div class="card store-card text-center">
          <div class="card-body p-3">
            <div class="store-logo bg-warning d-flex align-items-center justify-content-center text-white mx-auto mb-2">
              <i class="fas fa-home fa-2x"></i>
            </div>
            <h6 class="card-title mb-1">HomeDecor</h6>
            <div class="store-rating mb-2">4.7 ★</div>
            <small class="text-muted">Home & Garden</small>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-4 col-6 mb-3">
        <div class="card store-card text-center">
          <div class="card-body p-3">
            <div class="store-logo bg-danger d-flex align-items-center justify-content-center text-white mx-auto mb-2">
              <i class="fas fa-dumbbell fa-2x"></i>
            </div>
            <h6 class="card-title mb-1">SportZone</h6>
            <div class="store-rating mb-2">4.9 ★</div>
            <small class="text-muted">Sports</small>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-4 col-6 mb-3">
        <div class="card store-card text-center">
          <div class="card-body p-3">
            <div class="store-logo bg-info d-flex align-items-center justify-content-center text-white mx-auto mb-2">
              <i class="fas fa-book fa-2x"></i>
            </div>
            <h6 class="card-title mb-1">BookCorner</h6>
            <div class="store-rating mb-2">4.6 ★</div>
            <small class="text-muted">Books</small>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-4 col-6 mb-3">
        <div class="card store-card text-center">
          <div class="card-body p-3">
            <div class="store-logo bg-secondary d-flex align-items-center justify-content-center text-white mx-auto mb-2">
              <i class="fas fa-gamepad fa-2x"></i>
            </div>
            <h6 class="card-title mb-1">GameStore</h6>
            <div class="store-rating mb-2">4.8 ★</div>
            <small class="text-muted">Gaming</small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="row mb-4" id="directory">
    <div class="col-lg-6">
      <form method="get" class="d-flex">
        <input type="text" name="q" class="form-control me-2" placeholder="Search stores..." value="{{ request.GET.q }}">
        <button class="btn btn-primary" type="submit">
          <i class="fas fa-search"></i>
        </button>
      </form>
    </div>
    <div class="col-lg-3">
      <select class="form-select" name="sort" onchange="updateSort(this.value)">
        <option value="">Sort by...</option>
        <option value="name" {% if request.GET.sort == 'name' %}selected{% endif %}>Name A-Z</option>
        <option value="-rating" {% if request.GET.sort == '-rating' %}selected{% endif %}>Highest Rated</option>
        <option value="-product_count" {% if request.GET.sort == '-product_count' %}selected{% endif %}>Most Products</option>
        <option value="-created_at" {% if request.GET.sort == '-created_at' %}selected{% endif %}>Newest</option>
      </select>
    </div>
    <div class="col-lg-3">
      <div class="view-toggle btn-group w-100">
        <button type="button" class="btn active" onclick="setView('grid')">
          <i class="fas fa-th"></i> Grid
        </button>
        <button type="button" class="btn" onclick="setView('map')">
          <i class="fas fa-map"></i> Map
        </button>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Filters Sidebar -->
    <div class="col-lg-3">
      <div class="filter-sidebar">
        <h5 class="fw-bold mb-3">Filters</h5>
        
        <!-- Category Filter -->
        <div class="filter-section">
          <h6 class="fw-semibold">Category</h6>
          {% for category in categories %}
          <div class="form-check">
            <input class="form-check-input" type="checkbox" name="category" value="{{ category.id }}" id="cat{{ category.id }}">
            <label class="form-check-label" for="cat{{ category.id }}">
              {{ category.name }} ({{ category.store_count }})
            </label>
          </div>
          {% endfor %}
        </div>

        <!-- Location Filter -->
        <div class="filter-section">
          <h6 class="fw-semibold">Location</h6>
          <select class="form-select mb-2" name="country">
            <option value="">All Countries</option>
            <option value="US">United States</option>
            <option value="UK">United Kingdom</option>
            <option value="CA">Canada</option>
            <option value="AU">Australia</option>
            <option value="DE">Germany</option>
          </select>
          <input type="text" class="form-control" name="city" placeholder="City" value="{{ request.GET.city }}">
        </div>

        <!-- Rating Filter -->
        <div class="filter-section">
          <h6 class="fw-semibold">Rating</h6>
          <div class="form-check">
            <input class="form-check-input" type="radio" name="rating" value="4.5" id="rating45">
            <label class="form-check-label" for="rating45">
              <span class="text-warning">★★★★★</span> 4.5 & up
            </label>
          </div>
          <div class="form-check">
            <input class="form-check-input" type="radio" name="rating" value="4" id="rating4">
            <label class="form-check-label" for="rating4">
              <span class="text-warning">★★★★☆</span> 4.0 & up
            </label>
          </div>
          <div class="form-check">
            <input class="form-check-input" type="radio" name="rating" value="3" id="rating3">
            <label class="form-check-label" for="rating3">
              <span class="text-warning">★★★☆☆</span> 3.0 & up
            </label>
          </div>
        </div>

        <!-- Features Filter -->
        <div class="filter-section">
          <h6 class="fw-semibold">Features</h6>
          <div class="form-check">
            <input class="form-check-input" type="checkbox" name="verified" value="1" id="verified">
            <label class="form-check-label" for="verified">
              <i class="fas fa-check-circle text-success me-1"></i>Verified Stores
            </label>
          </div>
          <div class="form-check">
            <input class="form-check-input" type="checkbox" name="free_shipping" value="1" id="freeShipping">
            <label class="form-check-label" for="freeShipping">
              <i class="fas fa-shipping-fast text-primary me-1"></i>Free Shipping
            </label>
          </div>
          <div class="form-check">
            <input class="form-check-input" type="checkbox" name="returns" value="1" id="returns">
            <label class="form-check-label" for="returns">
              <i class="fas fa-undo text-info me-1"></i>Easy Returns
            </label>
          </div>
        </div>

        <button class="btn btn-primary w-100 mb-2" onclick="applyFilters()">Apply Filters</button>
        <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">Clear All</button>
      </div>
    </div>

    <!-- Stores Grid/Map -->
    <div class="col-lg-9">
      <!-- Results Info -->
      <div class="d-flex justify-content-between align-items-center mb-3">
        <p class="text-muted mb-0">
          Showing {{ stores.start_index }}-{{ stores.end_index }} of {{ stores.paginator.count }} stores
          {% if request.GET.q %} for "{{ request.GET.q }}"{% endif %}
        </p>
      </div>

      <!-- Grid View -->
      <div class="row" id="storesGrid">
        {% for store in stores %}
        <div class="col-lg-4 col-md-6 mb-4">
          <div class="card store-card">
            <div class="position-relative">
              {% if store.is_verified %}
                <span class="store-badge verified-badge">
                  <i class="fas fa-check-circle me-1"></i>Verified
                </span>
              {% endif %}
            </div>
            
            <div class="card-body text-center">
              {% if store.logo %}
                <img src="{{ store.logo }}" class="store-logo mb-3" alt="{{ store.name }}">
              {% else %}
                <div class="store-logo bg-primary d-flex align-items-center justify-content-center text-white mx-auto mb-3">
                  <i class="fas fa-store fa-2x"></i>
                </div>
              {% endif %}
              
              <h5 class="card-title">
                <a href="/stores/{{ store.id }}/" class="text-decoration-none text-dark">
                  {{ store.name }}
                </a>
              </h5>
              
              <p class="card-text text-muted small">{{ store.description|truncatechars:80 }}</p>
              
              <div class="d-flex justify-content-center align-items-center mb-2">
                <div class="store-rating me-2">{{ store.rating }} ★</div>
                <small class="text-muted">({{ store.review_count }} reviews)</small>
              </div>
              
              <div class="mb-3">
                <small class="text-muted">
                  <i class="fas fa-map-marker-alt me-1"></i>{{ store.location }}
                </small>
                <br>
                <small class="text-muted">
                  <i class="fas fa-box me-1"></i>{{ store.product_count }} products
                </small>
              </div>
              
              <div class="d-flex gap-2">
                <a href="/stores/{{ store.id }}/" class="btn btn-primary btn-sm flex-fill">
                  <i class="fas fa-eye me-1"></i>Visit Store
                </a>
                <button class="btn btn-outline-primary btn-sm" onclick="followStore({{ store.id }})">
                  <i class="fas fa-heart"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
        {% empty %}
        <div class="col-12">
          <div class="text-center py-5">
            <i class="fas fa-store fa-4x text-muted mb-3"></i>
            <h4>No stores found</h4>
            <p class="text-muted">Try adjusting your search criteria or filters</p>
            <a href="/stores/" class="btn btn-primary">View All Stores</a>
          </div>
        </div>
        {% endfor %}
      </div>

      <!-- Map View -->
      <div class="map-view" id="storesMap" style="display: none;">
        <div class="text-center">
          <i class="fas fa-map fa-4x mb-3"></i>
          <h5>Interactive Store Map</h5>
          <p class="text-muted">Map integration would be implemented here</p>
        </div>
      </div>

      <!-- Pagination -->
      {% if stores.has_other_pages %}
      <nav aria-label="Stores pagination" class="mt-4">
        <ul class="pagination justify-content-center">
          {% if stores.has_previous %}
            <li class="page-item">
              <a class="page-link" href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}page={{ stores.previous_page_number }}">
                <i class="fas fa-chevron-left"></i>
              </a>
            </li>
          {% endif %}
          
          {% for num in stores.paginator.page_range %}
            {% if stores.number == num %}
              <li class="page-item active">
                <span class="page-link">{{ num }}</span>
              </li>
            {% elif num > stores.number|add:'-3' and num < stores.number|add:'3' %}
              <li class="page-item">
                <a class="page-link" href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}page={{ num }}">{{ num }}</a>
              </li>
            {% endif %}
          {% endfor %}
          
          {% if stores.has_next %}
            <li class="page-item">
              <a class="page-link" href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}page={{ stores.next_page_number }}">
                <i class="fas fa-chevron-right"></i>
              </a>
            </li>
          {% endif %}
        </ul>
      </nav>
      {% endif %}
    </div>
  </div>
</div>

<script>
function updateSort(value) {
  const url = new URL(window.location);
  if (value) {
    url.searchParams.set('sort', value);
  } else {
    url.searchParams.delete('sort');
  }
  window.location.href = url.toString();
}

function setView(viewType) {
  // Toggle view buttons
  document.querySelectorAll('.view-toggle .btn').forEach(btn => btn.classList.remove('active'));
  event.target.closest('.btn').classList.add('active');
  
  // Toggle views
  const gridView = document.getElementById('storesGrid');
  const mapView = document.getElementById('storesMap');
  
  if (viewType === 'map') {
    gridView.style.display = 'none';
    mapView.style.display = 'block';
  } else {
    gridView.style.display = 'flex';
    mapView.style.display = 'none';
  }
}

function applyFilters() {
  const form = document.createElement('form');
  form.method = 'GET';
  
  // Collect all filter values
  const filters = document.querySelectorAll('.filter-sidebar input:checked, .filter-sidebar select, .filter-sidebar input[type="text"]');
  filters.forEach(filter => {
    if (filter.value) {
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = filter.name;
      input.value = filter.value;
      form.appendChild(input);
    }
  });
  
  // Preserve search query
  const searchQuery = document.querySelector('input[name="q"]').value;
  if (searchQuery) {
    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = 'q';
    input.value = searchQuery;
    form.appendChild(input);
  }
  
  document.body.appendChild(form);
  form.submit();
}

function clearFilters() {
  const url = new URL(window.location);
  const searchQuery = url.searchParams.get('q');
  url.search = '';
  if (searchQuery) {
    url.searchParams.set('q', searchQuery);
  }
  window.location.href = url.toString();
}

function followStore(storeId) {
  console.log('Follow store:', storeId);
  showToast('Store followed!', 'success');
}

function showToast(message, type) {
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} position-fixed`;
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
  toast.innerHTML = `
    ${message}
    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
  `;
  document.body.appendChild(toast);
  
  setTimeout(() => {
    if (toast.parentElement) {
      toast.remove();
    }
  }, 3000);
}
</script>
{% endblock %}
