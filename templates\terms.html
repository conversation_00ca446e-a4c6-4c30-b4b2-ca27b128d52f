{% extends 'base.html' %}
{% load static %}

{% block title %}Terms of Service - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="Read the Terms of Service for Best in Click. Understand your rights and responsibilities when using our e-commerce platform.">
<style>
  .terms-hero {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 3rem;
  }
  .terms-nav {
    position: sticky;
    top: 100px;
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    height: fit-content;
  }
  .terms-nav .nav-link {
    color: #6c757d;
    padding: 0.5rem 0;
    border: none;
    text-align: left;
  }
  .terms-nav .nav-link.active {
    color: #0d6efd;
    font-weight: 600;
  }
  .terms-section {
    margin-bottom: 3rem;
    padding-top: 2rem;
  }
  .terms-section h2 {
    color: #0d6efd;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
  }
  .terms-section h3 {
    color: #495057;
    margin-top: 2rem;
    margin-bottom: 1rem;
  }
  .highlight-box {
    background: #e3f0ff;
    border-left: 4px solid #0d6efd;
    padding: 1.5rem;
    border-radius: 0.5rem;
    margin: 1.5rem 0;
  }
  .last-updated {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 2rem;
    text-align: center;
  }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="terms-hero">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-8">
        <h1 class="display-4 fw-bold mb-4">Terms of Service</h1>
        <p class="lead mb-0">Please read these terms carefully before using Best in Click services.</p>
      </div>
      <div class="col-lg-4 text-center">
        <i class="fas fa-file-contract" style="font-size: 6rem; opacity: 0.3;"></i>
      </div>
    </div>
  </div>
</div>

<div class="container">
  <div class="last-updated">
    <p class="mb-0"><strong>Last Updated:</strong> January 15, 2024</p>
  </div>
  
  <div class="row">
    <!-- Navigation -->
    <div class="col-lg-3">
      <nav class="terms-nav">
        <h5 class="fw-bold mb-3">Contents</h5>
        <div class="nav nav-pills flex-column">
          <a class="nav-link active" href="#acceptance">1. Acceptance of Terms</a>
          <a class="nav-link" href="#description">2. Service Description</a>
          <a class="nav-link" href="#accounts">3. User Accounts</a>
          <a class="nav-link" href="#conduct">4. User Conduct</a>
          <a class="nav-link" href="#content">5. Content and Intellectual Property</a>
          <a class="nav-link" href="#privacy">6. Privacy Policy</a>
          <a class="nav-link" href="#payments">7. Payments and Transactions</a>
          <a class="nav-link" href="#disclaimers">8. Disclaimers</a>
          <a class="nav-link" href="#limitation">9. Limitation of Liability</a>
          <a class="nav-link" href="#termination">10. Termination</a>
          <a class="nav-link" href="#changes">11. Changes to Terms</a>
          <a class="nav-link" href="#contact">12. Contact Information</a>
        </div>
      </nav>
    </div>
    
    <!-- Content -->
    <div class="col-lg-9">
      <div class="terms-content">
        <!-- Section 1 -->
        <section id="acceptance" class="terms-section">
          <h2>1. Acceptance of Terms</h2>
          <p>By accessing and using Best in Click ("the Service"), you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.</p>
          
          <div class="highlight-box">
            <h5><i class="fas fa-exclamation-triangle text-warning me-2"></i>Important Notice</h5>
            <p class="mb-0">These terms constitute a legally binding agreement between you and Best in Click. Please read them carefully.</p>
          </div>
        </section>

        <!-- Section 2 -->
        <section id="description" class="terms-section">
          <h2>2. Service Description</h2>
          <p>Best in Click is an e-commerce platform that provides:</p>
          <ul>
            <li>Product discovery and comparison tools</li>
            <li>AI-powered recommendations</li>
            <li>Multi-store product aggregation</li>
            <li>User reviews and ratings</li>
            <li>Price tracking and alerts</li>
          </ul>
          
          <h3>2.1 Service Availability</h3>
          <p>We strive to maintain 99.9% uptime but cannot guarantee uninterrupted service. Maintenance windows will be announced in advance when possible.</p>
          
          <h3>2.2 Service Modifications</h3>
          <p>We reserve the right to modify, suspend, or discontinue any part of the service at any time with reasonable notice to users.</p>
        </section>

        <!-- Section 3 -->
        <section id="accounts" class="terms-section">
          <h2>3. User Accounts</h2>
          <p>To access certain features of the Service, you may be required to create an account.</p>
          
          <h3>3.1 Account Registration</h3>
          <ul>
            <li>You must provide accurate and complete information</li>
            <li>You must be at least 13 years old to create an account</li>
            <li>One person may not maintain multiple accounts</li>
            <li>You are responsible for maintaining account security</li>
          </ul>
          
          <h3>3.2 Account Responsibilities</h3>
          <p>You are responsible for all activities that occur under your account. You must notify us immediately of any unauthorized use of your account.</p>
        </section>

        <!-- Section 4 -->
        <section id="conduct" class="terms-section">
          <h2>4. User Conduct</h2>
          <p>You agree not to use the Service to:</p>
          <ul>
            <li>Violate any applicable laws or regulations</li>
            <li>Infringe on intellectual property rights</li>
            <li>Transmit harmful or malicious code</li>
            <li>Engage in fraudulent activities</li>
            <li>Harass or abuse other users</li>
            <li>Spam or send unsolicited communications</li>
          </ul>
          
          <div class="highlight-box">
            <h5><i class="fas fa-shield-alt text-success me-2"></i>Community Guidelines</h5>
            <p class="mb-0">We maintain a safe and respectful environment for all users. Violations may result in account suspension or termination.</p>
          </div>
        </section>

        <!-- Section 5 -->
        <section id="content" class="terms-section">
          <h2>5. Content and Intellectual Property</h2>
          
          <h3>5.1 User-Generated Content</h3>
          <p>By submitting content (reviews, comments, images), you grant us a non-exclusive, worldwide, royalty-free license to use, modify, and display such content.</p>
          
          <h3>5.2 Our Intellectual Property</h3>
          <p>All content, features, and functionality of the Service are owned by Best in Click and are protected by copyright, trademark, and other intellectual property laws.</p>
          
          <h3>5.3 Third-Party Content</h3>
          <p>Product information, images, and descriptions may be provided by third-party merchants. We are not responsible for the accuracy of such content.</p>
        </section>

        <!-- Section 6 -->
        <section id="privacy" class="terms-section">
          <h2>6. Privacy Policy</h2>
          <p>Your privacy is important to us. Our Privacy Policy explains how we collect, use, and protect your information when you use our Service.</p>
          
          <div class="highlight-box">
            <h5><i class="fas fa-user-shield text-primary me-2"></i>Data Protection</h5>
            <p class="mb-0">We comply with GDPR, CCPA, and other applicable privacy regulations. <a href="/privacy/">Read our full Privacy Policy</a>.</p>
          </div>
        </section>

        <!-- Section 7 -->
        <section id="payments" class="terms-section">
          <h2>7. Payments and Transactions</h2>
          
          <h3>7.1 Third-Party Transactions</h3>
          <p>Best in Click facilitates connections between users and merchants but is not a party to transactions between users and third-party sellers.</p>
          
          <h3>7.2 Payment Processing</h3>
          <p>Payments are processed by third-party payment processors. We do not store your payment information on our servers.</p>
          
          <h3>7.3 Refunds and Returns</h3>
          <p>Refund and return policies are determined by individual merchants. We may assist in dispute resolution but are not responsible for merchant policies.</p>
        </section>

        <!-- Section 8 -->
        <section id="disclaimers" class="terms-section">
          <h2>8. Disclaimers</h2>
          <p>THE SERVICE IS PROVIDED "AS IS" WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT.</p>
          
          <h3>8.1 Product Information</h3>
          <p>We strive for accuracy but cannot guarantee that product information, prices, or availability are error-free or current.</p>
          
          <h3>8.2 Third-Party Services</h3>
          <p>We are not responsible for the content, policies, or practices of third-party websites or services linked from our platform.</p>
        </section>

        <!-- Section 9 -->
        <section id="limitation" class="terms-section">
          <h2>9. Limitation of Liability</h2>
          <p>IN NO EVENT SHALL BEST IN CLICK BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING WITHOUT LIMITATION, LOSS OF PROFITS, DATA, USE, GOODWILL, OR OTHER INTANGIBLE LOSSES.</p>
          
          <div class="highlight-box">
            <h5><i class="fas fa-info-circle text-info me-2"></i>Maximum Liability</h5>
            <p class="mb-0">Our total liability to you for all claims shall not exceed the amount you paid us in the 12 months preceding the claim.</p>
          </div>
        </section>

        <!-- Section 10 -->
        <section id="termination" class="terms-section">
          <h2>10. Termination</h2>
          
          <h3>10.1 Termination by You</h3>
          <p>You may terminate your account at any time by contacting our support team or using the account deletion feature.</p>
          
          <h3>10.2 Termination by Us</h3>
          <p>We may terminate or suspend your account immediately, without prior notice, for conduct that we believe violates these Terms or is harmful to other users or our business.</p>
        </section>

        <!-- Section 11 -->
        <section id="changes" class="terms-section">
          <h2>11. Changes to Terms</h2>
          <p>We reserve the right to modify these terms at any time. We will notify users of significant changes via email or prominent notice on our website.</p>
          
          <p>Continued use of the Service after changes constitutes acceptance of the new terms.</p>
        </section>

        <!-- Section 12 -->
        <section id="contact" class="terms-section">
          <h2>12. Contact Information</h2>
          <p>If you have any questions about these Terms of Service, please contact us:</p>
          
          <div class="row">
            <div class="col-md-6">
              <h5>Email</h5>
              <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
              
              <h5>Phone</h5>
              <p><a href="tel:******-BEST-CLICK">+1 (800) BEST-CLICK</a></p>
            </div>
            <div class="col-md-6">
              <h5>Mailing Address</h5>
              <p>
                Best in Click Legal Department<br>
                123 Innovation Drive<br>
                San Francisco, CA 94105<br>
                United States
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</div>

<script>
// Smooth scrolling for navigation links
document.querySelectorAll('.terms-nav .nav-link').forEach(link => {
  link.addEventListener('click', function(e) {
    e.preventDefault();
    const targetId = this.getAttribute('href').substring(1);
    const targetElement = document.getElementById(targetId);
    
    if (targetElement) {
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
      
      // Update active link
      document.querySelectorAll('.terms-nav .nav-link').forEach(l => l.classList.remove('active'));
      this.classList.add('active');
    }
  });
});

// Update active link on scroll
window.addEventListener('scroll', function() {
  const sections = document.querySelectorAll('.terms-section');
  const navLinks = document.querySelectorAll('.terms-nav .nav-link');
  
  let current = '';
  sections.forEach(section => {
    const sectionTop = section.offsetTop - 150;
    if (scrollY >= sectionTop) {
      current = section.getAttribute('id');
    }
  });
  
  navLinks.forEach(link => {
    link.classList.remove('active');
    if (link.getAttribute('href').substring(1) === current) {
      link.classList.add('active');
    }
  });
});
</script>
{% endblock %}
