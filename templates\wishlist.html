{% extends 'base.html' %}

{% block title %}My Wishlist - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="View and manage your wishlist on Best in Click. Save products you love and get notified when prices drop.">
<style>
  .wishlist-header {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
  }
  .wishlist-header::before {
    content: '♥';
    position: absolute;
    top: -20px;
    right: -20px;
    font-size: 8rem;
    opacity: 0.1;
    transform: rotate(15deg);
  }
  .wishlist-stats {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
  }
  .stat-item {
    text-align: center;
    padding: 1rem;
  }
  .stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #dc3545;
    display: block;
  }
  .stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.5rem;
  }
  .wishlist-item {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
  }
  .wishlist-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.15);
  }
  .wishlist-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #dc3545, #fd7e14);
  }
  .product-image {
    height: 200px;
    object-fit: cover;
    border-radius: 0 0 1rem 1rem;
  }
  .price-change {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.25rem 0.75rem;
    border-radius: 2rem;
    font-size: 0.8rem;
    font-weight: bold;
  }
  .price-drop {
    background: #28a745;
    color: white;
  }
  .price-increase {
    background: #dc3545;
    color: white;
  }
  .wishlist-actions {
    position: absolute;
    top: 1rem;
    left: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  .wishlist-item:hover .wishlist-actions {
    opacity: 1;
  }
  .action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  .action-btn.remove {
    background: #dc3545;
  }
  .action-btn.compare {
    background: #17a2b8;
  }
  .action-btn.share {
    background: #6f42c1;
  }
  .action-btn:hover {
    transform: scale(1.1);
  }
  .empty-wishlist {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }
  .filter-bar {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  .view-toggle {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
  }
  .view-toggle .btn {
    border: none;
    background: transparent;
  }
  .view-toggle .btn.active {
    background: #dc3545;
    color: white;
  }
  .wishlist-sharing {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  .share-option {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    text-decoration: none;
    color: #495057;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
  }
  .share-option:hover {
    background: #e9ecef;
    color: #dc3545;
    text-decoration: none;
    transform: translateX(5px);
  }
  .share-option i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
  }
  .price-alert-badge {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    border-radius: 2rem;
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
    font-weight: bold;
  }
  .list-view .wishlist-item {
    margin-bottom: 1rem;
  }
  .list-view .product-image {
    height: 120px;
    width: 120px;
    border-radius: 0.5rem;
  }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
  <!-- Wishlist Header -->
  <div class="wishlist-header">
    <div class="row align-items-center">
      <div class="col-md-8">
        <h1 class="fw-bold mb-2">
          <i class="fas fa-heart me-3"></i>My Wishlist
        </h1>
        <p class="mb-0">Keep track of products you love and get notified when prices drop</p>
      </div>
      <div class="col-md-4 text-end">
        <button class="btn btn-light btn-lg" onclick="shareWishlist()">
          <i class="fas fa-share-alt me-2"></i>Share Wishlist
        </button>
      </div>
    </div>
  </div>

  <!-- Wishlist Statistics -->
  <div class="wishlist-stats">
    <div class="row">
      <div class="col-md-3">
        <div class="stat-item">
          <span class="stat-number">{{ wishlist_items.count }}</span>
          <div class="stat-label">Items in Wishlist</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stat-item">
          <span class="stat-number">${{ total_value|floatformat:0 }}</span>
          <div class="stat-label">Total Value</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stat-item">
          <span class="stat-number">{{ price_drops_count }}</span>
          <div class="stat-label">Price Drops This Week</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stat-item">
          <span class="stat-number">${{ potential_savings|floatformat:0 }}</span>
          <div class="stat-label">Potential Savings</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filter and View Options -->
  <div class="filter-bar">
    <div class="row align-items-center">
      <div class="col-md-4">
        <select class="form-select" id="categoryFilter" onchange="filterWishlist()">
          <option value="">All Categories</option>
          <option value="electronics">Electronics</option>
          <option value="fashion">Fashion</option>
          <option value="home">Home & Garden</option>
          <option value="sports">Sports</option>
          <option value="books">Books</option>
        </select>
      </div>
      
      <div class="col-md-4">
        <select class="form-select" id="sortBy" onchange="sortWishlist()">
          <option value="date_added">Recently Added</option>
          <option value="price_low">Price: Low to High</option>
          <option value="price_high">Price: High to Low</option>
          <option value="name">Name A-Z</option>
          <option value="price_drop">Biggest Price Drop</option>
        </select>
      </div>
      
      <div class="col-md-4">
        <div class="view-toggle btn-group w-100">
          <button type="button" class="btn active" onclick="setView('grid')">
            <i class="fas fa-th"></i> Grid
          </button>
          <button type="button" class="btn" onclick="setView('list')">
            <i class="fas fa-list"></i> List
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Wishlist Items -->
  {% if wishlist_items %}
    <div class="row" id="wishlistGrid">
      {% for item in wishlist_items %}
        <div class="col-lg-4 col-md-6 mb-4" data-category="{{ item.product.category.slug }}" data-price="{{ item.product.price }}">
          <div class="card wishlist-item">
            <!-- Price Change Indicator -->
            {% if item.price_change %}
              <div class="price-change {% if item.price_change < 0 %}price-drop{% else %}price-increase{% endif %}">
                {% if item.price_change < 0 %}
                  <i class="fas fa-arrow-down"></i> ${{ item.price_change|floatformat:0 }}
                {% else %}
                  <i class="fas fa-arrow-up"></i> +${{ item.price_change|floatformat:0 }}
                {% endif %}
              </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="wishlist-actions">
              <button class="action-btn remove" onclick="removeFromWishlist({{ item.id }})" title="Remove from wishlist">
                <i class="fas fa-times"></i>
              </button>
              <button class="action-btn compare" onclick="addToCompare({{ item.product.id }})" title="Add to compare">
                <i class="fas fa-balance-scale"></i>
              </button>
              <button class="action-btn share" onclick="shareProduct({{ item.product.id }})" title="Share product">
                <i class="fas fa-share-alt"></i>
              </button>
            </div>

            <!-- Product Image -->
            {% if item.product.image_url %}
              <img src="{{ item.product.image_url }}" class="card-img-top product-image" alt="{{ item.product.name }}">
            {% else %}
              <div class="product-image bg-light d-flex align-items-center justify-content-center">
                <i class="fas fa-image fa-3x text-muted"></i>
              </div>
            {% endif %}

            <div class="card-body">
              <h6 class="card-title">
                <a href="/products/{{ item.product.id }}/" class="text-decoration-none text-dark">
                  {{ item.product.name|truncatechars:50 }}
                </a>
              </h6>

              <div class="d-flex align-items-center mb-2">
                <div class="text-warning me-2">
                  {% for i in "12345" %}
                    {% if forloop.counter <= item.product.rating %}★{% else %}☆{% endif %}
                  {% endfor %}
                </div>
                <small class="text-muted">({{ item.product.review_count }} reviews)</small>
              </div>

              <div class="d-flex justify-content-between align-items-center mb-2">
                <div>
                  <span class="h6 text-primary fw-bold">${{ item.product.price }}</span>
                  {% if item.original_price and item.original_price != item.product.price %}
                    <small class="text-muted text-decoration-line-through ms-2">${{ item.original_price }}</small>
                  {% endif %}
                </div>
                {% if item.product.is_on_sale %}
                  <span class="badge bg-danger">On Sale</span>
                {% endif %}
              </div>

              {% if item.price_alert_enabled %}
                <div class="mb-2">
                  <span class="price-alert-badge">
                    <i class="fas fa-bell me-1"></i>Price Alert Active
                  </span>
                </div>
              {% endif %}

              <small class="text-muted">
                <i class="fas fa-heart me-1"></i>Added {{ item.created_at|timesince }} ago
              </small>
            </div>

            <div class="card-footer bg-transparent">
              <div class="d-flex gap-2">
                <a href="/products/{{ item.product.id }}/" class="btn btn-primary btn-sm flex-fill">
                  <i class="fas fa-eye me-1"></i>View Details
                </a>
                <button class="btn btn-outline-primary btn-sm" onclick="togglePriceAlert({{ item.id }})">
                  <i class="fas fa-bell"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      {% endfor %}
    </div>

    <!-- Load More -->
    {% if has_more_items %}
      <div class="text-center mt-4">
        <button class="btn btn-outline-primary btn-lg" onclick="loadMoreItems()">
          <i class="fas fa-plus me-2"></i>Load More Items
        </button>
      </div>
    {% endif %}

  {% else %}
    <!-- Empty Wishlist -->
    <div class="empty-wishlist">
      <i class="fas fa-heart-broken fa-5x text-muted mb-4"></i>
      <h3>Your Wishlist is Empty</h3>
      <p class="text-muted mb-4">Start adding products you love to keep track of them and get price alerts</p>
      
      <div class="d-flex justify-content-center gap-3 mb-4">
        <a href="/products/" class="btn btn-primary btn-lg">
          <i class="fas fa-shopping-bag me-2"></i>Browse Products
        </a>
        <a href="/recommendations/" class="btn btn-outline-primary btn-lg">
          <i class="fas fa-robot me-2"></i>Get Recommendations
        </a>
      </div>

      <!-- Wishlist Benefits -->
      <div class="row mt-5">
        <div class="col-md-4">
          <div class="text-center">
            <i class="fas fa-bell fa-2x text-primary mb-3"></i>
            <h6>Price Alerts</h6>
            <p class="text-muted small">Get notified when prices drop on your favorite items</p>
          </div>
        </div>
        <div class="col-md-4">
          <div class="text-center">
            <i class="fas fa-share-alt fa-2x text-success mb-3"></i>
            <h6>Share Lists</h6>
            <p class="text-muted small">Share your wishlist with friends and family</p>
          </div>
        </div>
        <div class="col-md-4">
          <div class="text-center">
            <i class="fas fa-sync-alt fa-2x text-warning mb-3"></i>
            <h6>Sync Across Devices</h6>
            <p class="text-muted small">Access your wishlist from any device, anywhere</p>
          </div>
        </div>
      </div>
    </div>
  {% endif %}

  <!-- Wishlist Sharing Options -->
  {% if wishlist_items %}
    <div class="wishlist-sharing" id="sharingOptions" style="display: none;">
      <h5 class="fw-bold mb-3">
        <i class="fas fa-share-alt me-2"></i>Share Your Wishlist
      </h5>
      
      <div class="row">
        <div class="col-md-6">
          <a href="#" class="share-option" onclick="shareVia('facebook')">
            <i class="fab fa-facebook-f"></i>Share on Facebook
          </a>
          <a href="#" class="share-option" onclick="shareVia('twitter')">
            <i class="fab fa-twitter"></i>Share on Twitter
          </a>
          <a href="#" class="share-option" onclick="shareVia('whatsapp')">
            <i class="fab fa-whatsapp"></i>Share on WhatsApp
          </a>
        </div>
        <div class="col-md-6">
          <a href="#" class="share-option" onclick="shareVia('email')">
            <i class="fas fa-envelope"></i>Share via Email
          </a>
          <a href="#" class="share-option" onclick="copyWishlistLink()">
            <i class="fas fa-link"></i>Copy Link
          </a>
          <a href="#" class="share-option" onclick="generatePDF()">
            <i class="fas fa-file-pdf"></i>Export as PDF
          </a>
        </div>
      </div>
    </div>
  {% endif %}
</div>

<script>
function removeFromWishlist(itemId) {
  if (confirm('Are you sure you want to remove this item from your wishlist?')) {
    console.log('Removing item:', itemId);
    
    // Remove the item from DOM
    const item = document.querySelector(`[data-item-id="${itemId}"]`);
    if (item) {
      item.style.opacity = '0';
      item.style.transform = 'scale(0.8)';
      setTimeout(() => {
        item.remove();
        updateWishlistStats();
      }, 300);
    }
    
    showToast('Item removed from wishlist', 'success');
  }
}

function addToCompare(productId) {
  console.log('Adding to compare:', productId);
  showToast('Added to comparison!', 'info');
}

function shareProduct(productId) {
  console.log('Sharing product:', productId);
  if (navigator.share) {
    navigator.share({
      title: 'Check out this product',
      url: `/products/${productId}/`
    });
  } else {
    showToast('Product link copied to clipboard!', 'success');
  }
}

function togglePriceAlert(itemId) {
  console.log('Toggling price alert for:', itemId);
  showToast('Price alert updated!', 'success');
}

function filterWishlist() {
  const category = document.getElementById('categoryFilter').value;
  const items = document.querySelectorAll('#wishlistGrid > div');
  
  items.forEach(item => {
    if (!category || item.dataset.category === category) {
      item.style.display = 'block';
    } else {
      item.style.display = 'none';
    }
  });
}

function sortWishlist() {
  const sortBy = document.getElementById('sortBy').value;
  const grid = document.getElementById('wishlistGrid');
  const items = Array.from(grid.children);
  
  items.sort((a, b) => {
    switch (sortBy) {
      case 'price_low':
        return parseFloat(a.dataset.price) - parseFloat(b.dataset.price);
      case 'price_high':
        return parseFloat(b.dataset.price) - parseFloat(a.dataset.price);
      case 'name':
        return a.querySelector('.card-title a').textContent.localeCompare(
          b.querySelector('.card-title a').textContent
        );
      default:
        return 0;
    }
  });
  
  items.forEach(item => grid.appendChild(item));
}

function setView(viewType) {
  // Toggle view buttons
  document.querySelectorAll('.view-toggle .btn').forEach(btn => btn.classList.remove('active'));
  event.target.closest('.btn').classList.add('active');
  
  const grid = document.getElementById('wishlistGrid');
  if (viewType === 'list') {
    grid.classList.add('list-view');
  } else {
    grid.classList.remove('list-view');
  }
}

function shareWishlist() {
  const sharingOptions = document.getElementById('sharingOptions');
  if (sharingOptions.style.display === 'none') {
    sharingOptions.style.display = 'block';
    sharingOptions.scrollIntoView({ behavior: 'smooth' });
  } else {
    sharingOptions.style.display = 'none';
  }
}

function shareVia(platform) {
  console.log('Sharing via:', platform);
  showToast(`Sharing via ${platform}...`, 'info');
}

function copyWishlistLink() {
  const link = window.location.href;
  navigator.clipboard.writeText(link);
  showToast('Wishlist link copied to clipboard!', 'success');
}

function generatePDF() {
  console.log('Generating PDF...');
  showToast('PDF export feature coming soon!', 'info');
}

function loadMoreItems() {
  console.log('Loading more items...');
  showToast('Loading more items...', 'info');
}

function updateWishlistStats() {
  // Update statistics after item removal
  const remainingItems = document.querySelectorAll('#wishlistGrid > div:not([style*="display: none"])').length;
  document.querySelector('.stat-number').textContent = remainingItems;
}

function showToast(message, type) {
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} position-fixed`;
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
  toast.innerHTML = `
    ${message}
    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
  `;
  document.body.appendChild(toast);
  
  setTimeout(() => {
    if (toast.parentElement) {
      toast.remove();
    }
  }, 3000);
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
  // Add data attributes for item identification
  document.querySelectorAll('.wishlist-item').forEach((item, index) => {
    item.closest('.col-lg-4').setAttribute('data-item-id', index + 1);
  });
});
</script>
{% endblock %}
