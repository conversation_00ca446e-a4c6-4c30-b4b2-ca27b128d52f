{% extends 'base.html' %}

{% block title %}Write Review - Best in Click{% endblock %}

{% block extra_head %}
<meta name="description" content="Write a review for your purchased product on Best in Click. Share your experience with other customers.">
<style>
  .write-review-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
  }
  .review-form {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-bottom: 2rem;
  }
  .product-info {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  .product-image {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 0.5rem;
  }
  .rating-input {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }
  .star-input {
    font-size: 2rem;
    color: #dee2e6;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  .star-input:hover,
  .star-input.active {
    color: #ffc107;
    transform: scale(1.1);
  }
  .form-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e9ecef;
  }
  .form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }
  .section-title {
    color: #0d6efd;
    font-weight: bold;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
  }
  .section-title i {
    margin-right: 0.5rem;
  }
  .photo-upload {
    border: 2px dashed #dee2e6;
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
  }
  .photo-upload:hover {
    border-color: #0d6efd;
    background: #e3f0ff;
  }
  .photo-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
  }
  .photo-item {
    position: relative;
    width: 100px;
    height: 100px;
  }
  .photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0.5rem;
  }
  .photo-remove {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 24px;
    height: 24px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 0.8rem;
    cursor: pointer;
  }
  .review-guidelines {
    background: #e3f0ff;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  .guideline-item {
    display: flex;
    align-items: start;
    margin-bottom: 1rem;
  }
  .guideline-item:last-child {
    margin-bottom: 0;
  }
  .guideline-icon {
    width: 24px;
    height: 24px;
    background: #0d6efd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    margin-right: 1rem;
    flex-shrink: 0;
  }
  .character-count {
    font-size: 0.9rem;
    color: #6c757d;
    text-align: right;
    margin-top: 0.5rem;
  }
  .character-count.warning {
    color: #ffc107;
  }
  .character-count.danger {
    color: #dc3545;
  }
  .pros-cons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
  .pros-cons-item {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
  }
  .pros-cons-item.pros {
    border-left: 4px solid #28a745;
  }
  .pros-cons-item.cons {
    border-left: 4px solid #dc3545;
  }
  .tag-input {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.5rem;
    min-height: 45px;
    cursor: text;
  }
  .tag-item {
    background: #0d6efd;
    color: white;
    border-radius: 2rem;
    padding: 0.25rem 0.75rem;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
  }
  .tag-remove {
    background: none;
    border: none;
    color: white;
    margin-left: 0.5rem;
    cursor: pointer;
  }
  .tag-input input {
    border: none;
    outline: none;
    flex: 1;
    min-width: 100px;
  }
  .review-preview {
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-top: 2rem;
    display: none;
  }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
  <!-- Header -->
  <div class="write-review-header">
    <div class="row align-items-center">
      <div class="col-md-8">
        <h1 class="fw-bold mb-2">
          <i class="fas fa-edit me-3"></i>Write a Review
        </h1>
        <p class="mb-0">Share your experience and help other customers make informed decisions</p>
      </div>
      <div class="col-md-4 text-end">
        <a href="/reviews/" class="btn btn-light">
          <i class="fas fa-arrow-left me-2"></i>Back to Reviews
        </a>
      </div>
    </div>
  </div>

  <!-- Review Guidelines -->
  <div class="review-guidelines">
    <h5 class="fw-bold mb-3">
      <i class="fas fa-info-circle me-2"></i>Review Guidelines
    </h5>
    
    <div class="row">
      <div class="col-md-6">
        <div class="guideline-item">
          <div class="guideline-icon">
            <i class="fas fa-check"></i>
          </div>
          <div>
            <strong>Be honest and helpful</strong><br>
            <small class="text-muted">Share your genuine experience with the product</small>
          </div>
        </div>
        
        <div class="guideline-item">
          <div class="guideline-icon">
            <i class="fas fa-camera"></i>
          </div>
          <div>
            <strong>Add photos</strong><br>
            <small class="text-muted">Pictures help other customers see the product in use</small>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="guideline-item">
          <div class="guideline-icon">
            <i class="fas fa-heart"></i>
          </div>
          <div>
            <strong>Be respectful</strong><br>
            <small class="text-muted">Keep your review constructive and appropriate</small>
          </div>
        </div>
        
        <div class="guideline-item">
          <div class="guideline-icon">
            <i class="fas fa-star"></i>
          </div>
          <div>
            <strong>Rate fairly</strong><br>
            <small class="text-muted">Consider all aspects: quality, value, and experience</small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Review Form -->
    <div class="col-lg-8">
      <form method="post" enctype="multipart/form-data" id="reviewForm">
        {% csrf_token %}
        
        <div class="review-form">
          <!-- Product Selection -->
          <div class="form-section">
            <h4 class="section-title">
              <i class="fas fa-box"></i>Select Product
            </h4>
            
            {% if product %}
              <!-- Pre-selected Product -->
              <div class="product-info">
                <div class="row align-items-center">
                  <div class="col-md-2">
                    {% if product.image_url %}
                      <img src="{{ product.image_url }}" class="product-image" alt="{{ product.name }}">
                    {% else %}
                      <div class="product-image bg-light d-flex align-items-center justify-content-center">
                        <i class="fas fa-image fa-2x text-muted"></i>
                      </div>
                    {% endif %}
                  </div>
                  <div class="col-md-10">
                    <h6 class="mb-1">{{ product.name }}</h6>
                    <p class="text-muted mb-1">{{ product.category.name }}</p>
                    <small class="text-muted">Purchased on {{ purchase_date|date:"M d, Y" }}</small>
                  </div>
                </div>
              </div>
              <input type="hidden" name="product" value="{{ product.id }}">
            {% else %}
              <!-- Product Selection -->
              <select class="form-select form-select-lg" name="product" required>
                <option value="">Choose a product you've purchased...</option>
                {% for purchase in user_purchases %}
                  <option value="{{ purchase.product.id }}">{{ purchase.product.name }} - Purchased {{ purchase.created_at|date:"M d, Y" }}</option>
                {% endfor %}
              </select>
            {% endif %}
          </div>

          <!-- Rating -->
          <div class="form-section">
            <h4 class="section-title">
              <i class="fas fa-star"></i>Overall Rating
            </h4>
            
            <div class="rating-input" id="ratingInput">
              <span class="star-input" data-rating="1">★</span>
              <span class="star-input" data-rating="2">★</span>
              <span class="star-input" data-rating="3">★</span>
              <span class="star-input" data-rating="4">★</span>
              <span class="star-input" data-rating="5">★</span>
            </div>
            <input type="hidden" name="rating" id="ratingValue" required>
            <small class="text-muted">Click on the stars to rate this product</small>
          </div>

          <!-- Review Title -->
          <div class="form-section">
            <h4 class="section-title">
              <i class="fas fa-heading"></i>Review Title
            </h4>
            
            <input type="text" class="form-control form-control-lg" name="title" 
                   placeholder="Summarize your experience in a few words..." 
                   maxlength="100" required>
            <div class="character-count">
              <span id="titleCount">0</span>/100 characters
            </div>
          </div>

          <!-- Review Text -->
          <div class="form-section">
            <h4 class="section-title">
              <i class="fas fa-comment"></i>Your Review
            </h4>
            
            <textarea class="form-control" name="text" rows="6" 
                      placeholder="Tell others about your experience with this product. What did you like or dislike? How did you use it? What should others know before buying?"
                      maxlength="2000" required></textarea>
            <div class="character-count">
              <span id="textCount">0</span>/2000 characters
            </div>
          </div>

          <!-- Pros and Cons -->
          <div class="form-section">
            <h4 class="section-title">
              <i class="fas fa-balance-scale"></i>Pros & Cons (Optional)
            </h4>
            
            <div class="pros-cons">
              <div class="pros-cons-item pros">
                <h6 class="text-success mb-2">
                  <i class="fas fa-thumbs-up me-2"></i>What you liked
                </h6>
                <textarea class="form-control" name="pros" rows="3" 
                          placeholder="List the positive aspects..."></textarea>
              </div>
              
              <div class="pros-cons-item cons">
                <h6 class="text-danger mb-2">
                  <i class="fas fa-thumbs-down me-2"></i>What could be better
                </h6>
                <textarea class="form-control" name="cons" rows="3" 
                          placeholder="List areas for improvement..."></textarea>
              </div>
            </div>
          </div>

          <!-- Photos -->
          <div class="form-section">
            <h4 class="section-title">
              <i class="fas fa-camera"></i>Add Photos (Optional)
            </h4>
            
            <div class="photo-upload" onclick="document.getElementById('photoInput').click()">
              <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
              <h6>Click to upload photos</h6>
              <p class="text-muted mb-0">JPG, PNG up to 5MB each (max 5 photos)</p>
            </div>
            
            <input type="file" id="photoInput" name="photos" multiple accept="image/*" style="display: none;" onchange="previewPhotos(this)">
            
            <div class="photo-preview" id="photoPreview"></div>
          </div>

          <!-- Tags -->
          <div class="form-section">
            <h4 class="section-title">
              <i class="fas fa-tags"></i>Tags (Optional)
            </h4>
            
            <div class="tag-input" id="tagInput" onclick="focusTagInput()">
              <input type="text" placeholder="Add tags (press Enter)" onkeypress="addTag(event)">
            </div>
            <small class="text-muted">Add relevant tags to help others find your review</small>
          </div>

          <!-- Submit Buttons -->
          <div class="d-flex justify-content-between">
            <button type="button" class="btn btn-outline-secondary" onclick="previewReview()">
              <i class="fas fa-eye me-2"></i>Preview
            </button>
            
            <div>
              <button type="button" class="btn btn-outline-primary me-2" onclick="saveDraft()">
                <i class="fas fa-save me-2"></i>Save Draft
              </button>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-paper-plane me-2"></i>Submit Review
              </button>
            </div>
          </div>
        </div>
      </form>

      <!-- Review Preview -->
      <div class="review-preview" id="reviewPreview">
        <h5 class="fw-bold mb-3">Review Preview</h5>
        <div id="previewContent"></div>
        <button type="button" class="btn btn-secondary mt-3" onclick="hidePreview()">
          <i class="fas fa-times me-2"></i>Close Preview
        </button>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
      <!-- Tips -->
      <div class="card">
        <div class="card-header">
          <h6 class="fw-bold mb-0">
            <i class="fas fa-lightbulb me-2"></i>Writing Tips
          </h6>
        </div>
        <div class="card-body">
          <ul class="list-unstyled mb-0">
            <li class="mb-2">
              <i class="fas fa-check text-success me-2"></i>
              <small>Be specific about your experience</small>
            </li>
            <li class="mb-2">
              <i class="fas fa-check text-success me-2"></i>
              <small>Mention how long you've used the product</small>
            </li>
            <li class="mb-2">
              <i class="fas fa-check text-success me-2"></i>
              <small>Include both positives and negatives</small>
            </li>
            <li class="mb-2">
              <i class="fas fa-check text-success me-2"></i>
              <small>Add photos to support your review</small>
            </li>
            <li class="mb-0">
              <i class="fas fa-check text-success me-2"></i>
              <small>Keep it helpful and constructive</small>
            </li>
          </ul>
        </div>
      </div>

      <!-- Reviewer Stats -->
      <div class="card mt-3">
        <div class="card-header">
          <h6 class="fw-bold mb-0">
            <i class="fas fa-user me-2"></i>Your Reviewer Stats
          </h6>
        </div>
        <div class="card-body">
          <div class="text-center">
            <div class="mb-2">
              <span class="h4 text-primary">{{ user.reviews.count|default:0 }}</span>
              <br><small class="text-muted">Reviews Written</small>
            </div>
            <div class="mb-2">
              <span class="h4 text-warning">{{ user.reviewer_rank|default:"Beginner" }}</span>
              <br><small class="text-muted">Reviewer Level</small>
            </div>
            <div class="mb-0">
              <span class="h4 text-success">{{ user.helpful_votes|default:0 }}</span>
              <br><small class="text-muted">Helpful Votes</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
let selectedRating = 0;
let uploadedPhotos = [];
let tags = [];

// Rating functionality
document.querySelectorAll('.star-input').forEach(star => {
  star.addEventListener('click', function() {
    selectedRating = parseInt(this.dataset.rating);
    document.getElementById('ratingValue').value = selectedRating;
    
    document.querySelectorAll('.star-input').forEach((s, index) => {
      if (index < selectedRating) {
        s.classList.add('active');
      } else {
        s.classList.remove('active');
      }
    });
  });
  
  star.addEventListener('mouseover', function() {
    const rating = parseInt(this.dataset.rating);
    document.querySelectorAll('.star-input').forEach((s, index) => {
      if (index < rating) {
        s.style.color = '#ffc107';
      } else {
        s.style.color = '#dee2e6';
      }
    });
  });
});

document.getElementById('ratingInput').addEventListener('mouseleave', function() {
  document.querySelectorAll('.star-input').forEach((s, index) => {
    if (index < selectedRating) {
      s.style.color = '#ffc107';
    } else {
      s.style.color = '#dee2e6';
    }
  });
});

// Character counting
document.querySelector('input[name="title"]').addEventListener('input', function() {
  const count = this.value.length;
  const counter = document.getElementById('titleCount');
  counter.textContent = count;
  
  if (count > 80) {
    counter.parentElement.classList.add('warning');
  } else {
    counter.parentElement.classList.remove('warning');
  }
});

document.querySelector('textarea[name="text"]').addEventListener('input', function() {
  const count = this.value.length;
  const counter = document.getElementById('textCount');
  counter.textContent = count;
  
  if (count > 1800) {
    counter.parentElement.classList.add('danger');
  } else if (count > 1500) {
    counter.parentElement.classList.add('warning');
  } else {
    counter.parentElement.classList.remove('warning', 'danger');
  }
});

// Photo upload
function previewPhotos(input) {
  const preview = document.getElementById('photoPreview');
  preview.innerHTML = '';
  
  Array.from(input.files).forEach((file, index) => {
    if (index >= 5) return; // Max 5 photos
    
    const reader = new FileReader();
    reader.onload = function(e) {
      const photoItem = document.createElement('div');
      photoItem.className = 'photo-item';
      photoItem.innerHTML = `
        <img src="${e.target.result}" alt="Review photo">
        <button type="button" class="photo-remove" onclick="removePhoto(${index})">×</button>
      `;
      preview.appendChild(photoItem);
    };
    reader.readAsDataURL(file);
  });
}

function removePhoto(index) {
  // Remove photo logic would go here
  event.target.parentElement.remove();
}

// Tags functionality
function focusTagInput() {
  document.querySelector('#tagInput input').focus();
}

function addTag(event) {
  if (event.key === 'Enter') {
    event.preventDefault();
    const input = event.target;
    const tagText = input.value.trim();
    
    if (tagText && !tags.includes(tagText)) {
      tags.push(tagText);
      
      const tagItem = document.createElement('div');
      tagItem.className = 'tag-item';
      tagItem.innerHTML = `
        ${tagText}
        <button type="button" class="tag-remove" onclick="removeTag('${tagText}')">×</button>
      `;
      
      input.parentElement.insertBefore(tagItem, input);
      input.value = '';
    }
  }
}

function removeTag(tagText) {
  tags = tags.filter(tag => tag !== tagText);
  event.target.parentElement.remove();
}

// Preview functionality
function previewReview() {
  const preview = document.getElementById('reviewPreview');
  const content = document.getElementById('previewContent');
  
  const title = document.querySelector('input[name="title"]').value;
  const text = document.querySelector('textarea[name="text"]').value;
  const rating = selectedRating;
  
  content.innerHTML = `
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-start mb-2">
          <h6>${title || 'Review Title'}</h6>
          <div class="rating-stars">
            ${'★'.repeat(rating)}${'☆'.repeat(5-rating)}
          </div>
        </div>
        <p>${text || 'Review text will appear here...'}</p>
        <small class="text-muted">Preview - This is how your review will look</small>
      </div>
    </div>
  `;
  
  preview.style.display = 'block';
  preview.scrollIntoView({ behavior: 'smooth' });
}

function hidePreview() {
  document.getElementById('reviewPreview').style.display = 'none';
}

function saveDraft() {
  console.log('Saving draft...');
  showToast('Draft saved successfully!', 'success');
}

// Form submission
document.getElementById('reviewForm').addEventListener('submit', function(e) {
  if (selectedRating === 0) {
    e.preventDefault();
    showToast('Please select a rating', 'danger');
    return;
  }
  
  const submitBtn = this.querySelector('button[type="submit"]');
  const originalText = submitBtn.innerHTML;
  
  submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Submitting...';
  submitBtn.disabled = true;
  
  // Re-enable button after 3 seconds (in case of error)
  setTimeout(() => {
    submitBtn.innerHTML = originalText;
    submitBtn.disabled = false;
  }, 3000);
});

function showToast(message, type) {
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} position-fixed`;
  toast.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
  toast.innerHTML = `
    ${message}
    <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
  `;
  document.body.appendChild(toast);
  
  setTimeout(() => {
    if (toast.parentElement) {
      toast.remove();
    }
  }, 3000);
}
</script>
{% endblock %}
