شرح مسار /dashboard/products/ في مشروع Django:

1. المسار الكامل: /api/dashboard/products/
2. الهدف: تمكين مالك المتجر (owner) من إدارة منتجات متجره (عرض، إضافة، تعديل، حذف) من خلال لوحة تحكم المالك.
3. من له الحق؟ فقط المستخدم الذي نوعه owner (مالك متجر) ومسجل الدخول (Authenticated)، أو الأدمن (admin).
4. الاستخدامات:
   - GET: جلب جميع المنتجات الخاصة بالمالك الحالي (قائمة المنتجات في لوحة التحكم).
   - POST: إنشاء منتج جديد وربطه تلقائيًا بمتجر المالك الحالي.
   - PUT/PATCH/DELETE: (غالبًا عبر /dashboard/products/<id>/) لتعديل أو حذف منتج معين.

مثال عملي:

1. جلب المنتجات (GET):
GET /api/dashboard/products/
Authorization: Bearer <access_token>

2. إنشاء منتج جديد (POST):
POST /api/dashboard/products/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "منتج جديد",
  "price": 100,
  "original_price": 90,
  "category_id": "uuid-التصنيف",
  "brand_id": "uuid-البراند",
  "description": "وصف المنتج",
  "image_url": "https://...",
  "video_url": "https://...",
  "release_date": "2025-06-19",
  "is_active": true
}

ملاحظة هامة:
- لا ترسل الحقول in_stock أو stock عند إنشاء أو تعديل منتج، لأن النظام لن يقبلها وسيظهر خطأ 500.

سياسة الوصول:
- يجب أن يكون المستخدم مالك متجر (owner) ومسجل الدخول.
- إذا لم يكن لديك متجر، ستحصل على رسالة خطأ.
- لا يمكن للعميل (customer) أو الزائر استخدام هذه النقطة.

ملخص:
- /dashboard/products/ = إدارة منتجات المالك (عرض/إضافة/تعديل/حذف) من خلال لوحة التحكم.
- يتطلب توكن وصلاحية مالك متجر.
- يُستخدم في الواجهة الخلفية للوحة تحكم المالك وليس للزوار أو العملاء.
